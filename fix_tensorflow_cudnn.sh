#!/bin/bash

echo "🔧 TensorFlow cuDNN兼容性修复脚本"
echo "=================================="

# 激活conda环境
echo "📦 激活llm环境..."
source ~/anaconda3/etc/profile.d/conda.sh
conda activate llm

echo "📋 当前环境信息:"
python -c "
import tensorflow as tf
print(f'  TensorFlow版本: {tf.__version__}')
try:
    build_info = tf.sysconfig.get_build_info()
    print(f'  编译时cuDNN: {build_info.get(\"cudnn_version\", \"未知\")}')
except:
    pass
"

echo ""
echo "🔄 开始降级TensorFlow..."

# 卸载当前TensorFlow
echo "1️⃣ 卸载当前TensorFlow..."
pip uninstall tensorflow tensorflow-gpu -y

# 安装兼容版本
echo "2️⃣ 安装TensorFlow 2.15.1..."
pip install tensorflow==2.15.1

# 验证安装
echo "3️⃣ 验证安装..."
python -c "
import tensorflow as tf
print(f'✅ TensorFlow版本: {tf.__version__}')

# 检查GPU
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    print(f'✅ 检测到 {len(gpus)} 个GPU')
else:
    print('⚠️  未检测到GPU')

# 测试cuDNN兼容性
try:
    import numpy as np
    test_input = tf.random.normal((1, 10, 20))
    test_lstm = tf.keras.layers.LSTM(32)
    output = test_lstm(test_input)
    print('✅ cuDNN LSTM测试成功')
except Exception as e:
    print(f'❌ cuDNN LSTM测试失败: {e}')
"

echo ""
echo "🎉 修复完成！"
echo "💡 如果仍有问题，可以尝试:"
echo "   - 重启Python内核"
echo "   - 清理缓存: pip cache purge"
echo "   - 重新安装: pip install --force-reinstall tensorflow==2.15.1"
