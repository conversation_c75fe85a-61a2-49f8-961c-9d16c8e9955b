# 航空公司机型分配优化项目 - 需求预测核心目标

**分析日期**: 2025-07-31  
**项目阶段**: 需求预测模型开发前分析  
**文档用途**: 明确7个核心预测目标，为机型分配优化模型提供关键输入

---

## 📋 执行摘要

基于对数据文件的深入分析和对项目需求的理解，本报告明确需求预测阶段的**7个核心预测目标**。这些预测目标直接服务于后续的MILP优化模型，为815个航班与211架飞机的优化分配提供可靠的座位容量约束数据支持。

预测的核心目标是**每个航班的座位需求数量**和**各舱位等级的座位需求量**，通过分层预测策略确保数据质量和预测精度，为优化模型提供高质量的输入。

---

## 🎯 7个核心预测目标详解

### 🎯 **目标1: 航班总需求预测** (优先级: 🔴 极高)

**预测内容**: 每个航班的总体座位需求数量

**具体说明**:
```
针对815个航班中的每一个航班，预测:
- 未来时期的总乘客预订数量
- 不考虑舱位等级差异的总体需求

示例输出:
{
  "flight_id": "AA0001",
  "route": "BER-BOD", 
  "predicted_total_demand": 42.7,
  "confidence_lower": 38.5,
  "confidence_upper": 46.9
}
```

**数据依据**:
- 航班基础特征: deptime, arrtime, origin, destination
- 航线网络特征: 机场枢纽度、航线距离、市场竞争
- 时序特征: RD0-RD330的总和与分布模式
- 市场特征: 航线市场份额、竞争环境

**业务价值**:
1. **优化模型目标函数**: 总收益 = Σ(各舱位实际运输量 × 票价)
2. **资源分配基础**: 为机型容量约束提供总的需求数据
3. **收益管理支撑**: 为动态定价和收益优化提供基础

### 🎯 **目标2: 分舱位需求预测** (优先级: 🔴 极高)

**预测内容**: 每个航班上11个舱位等级各自的座位需求数量

**具体说明**:
```
针对每个航班+舱位等级组合，预测:
- B类(超级经济舱)、G类、H类、K类、L类、M类、
- Q类(折扣经济舱)、S类(特价经济舱)、T类、V类、Y类

示例输出:
{
  "flight_id": "AA0001",
  "route": "BER-BOD",
  "cabin_demands": {
    "B": {"demand": 12.5, "confidence_level": 0.92},
    "G": {"demand": 18.3, "confidence_level": 0.89},
    "H": {"demand": 15.7, "confidence_level": 0.91},
    "K": {"demand": 8.2, "confidence_level": 0.85},
    "L": {"demand": 6.8, "confidence_level": 0.87},
    "M": {"demand": 20.1, "confidence_level": 0.90},
    "Q": {"demand": 25.4, "confidence_level": 0.88},
    "S": {"demand": 32.7, "confidence_level": 0.86},
    "T": {"demand": 14.9, "confidence_level": 0.84},
    "V": {"demand": 19.6, "confidence_level": 0.85},
    "Y": {"demand": 22.8, "confidence_level": 0.87}
  }
}
```

**数据依据**:
- 舱位特定数据: Class, Fare,舱位历史表现
- 价格弹性特征: 不同舱位等级的价格敏感度
- 时序特征: 各舱位等级的RD0-RD330预订模式
- 机型约束特征: 不同机型的舱位配置限制

**业务价值**:
1. **座位容量约束**: 确保分配机型各舱位容量 ≥ 预测需求
2. **舱位收入计算**: 为各舱位收益提供精确数据
3. **舱位配置优化**: 指导机队的舱位配置决策

### 🎯 **目标3: 预订曲线预测** (优先级: 🟡 中等)

**预测内容**: 31个预订距离点(RD0-RD330)的需求分布

**具体说明**:
```
针对每个航班+舱位等级组合，预测:
- RD0(当天预订)到RD330(提前330天)的预订量分布
- 预订速度和累积预订模式

示例输出:
{
  "flight_id": "AA0001",
  "route": "BER-BOD",
  "class": "B",
  "booking_curve": {
    "RD0": 0.0, "RD1": 0.67, "RD2": 0.0, "RD4": 1.29,
    "RD6": 1.0, "RD7": 0.0, "RD8": 0.0, "RD11": 0.33,
    "RD14": 0.0, "RD16": 0.0, "RD18": 0.33, "RD21": 0.0,
    "RD24": 0.0, "RD28": 0.0, "RD33": 0.0, "RD39": 0.0,
    "RD44": 0.0, "RD49": 0.0, "RD59": 0.0, "RD69": 0.0,
    "RD79": 0.0, "RD89": 0.67, "RD99": 0.0, "RD119": 0.0,
    "RD139": 0.0, "RD159": 0.0, "RD179": 0.0, "RD209": 0.0,
    "RD239": 0.0, "RD269": 0.0, "RD330": 0.0
  }
}
```

**数据依据**:
- RD时序数据: 完整的31个预订距离点历史数据
- 时间特征: 预订窗口的分类、季节性模式
- 行为特征: 到达/前预订比例、最后一分钟预订模式

**业务价值**:
1. **动态收益管理**: 支持不同预订阶段的差异化定价
2. **需求波动分析**: 为风险评估提供时序数据支撑
3. **库存管理**: 优化不同时间段的可售票数分配

### 🎯 **目标4: 经停航班整体需求预测** (优先级: 🟡 中等)

**预测内容**: 经停航班的全航段总座位需求

**具体说明**:
```
针对多航段经停航班，预测:
- 全航段的整体需求量(而非单个航段)
- 确保所有航段使用同一机型的总需求约束

示例输出:
{
  "flight_id": "AA0055",
  "route_type": "multi_stop",
  "segments": [
    {"from": "BOD", "to": "CSX", "flight_num": "AA0055BODCSX"},
    {"from": "BOD", "to": "GBJ", "flight_num": "AA0055BODGBJ"},
    {"from": "GBJ", "to": "CSX", "flight_num": "AA0055GBJCSX"}
  ],
  "total_demand": 67.3,
  "constraint_requirement": "所有航段必须使用相同机型"
}
```

**数据依据**:
- 经停航班标识: Flight1, Flight2, Flight3的配对关系
- 航段连接特征: 各航段间的逻辑依赖
- 全程需求模式: 经停航班的完整旅行需求

**业务价值**:
1. **连续性约束保证**: 确保经停航班机型分配的一致性
2. **整体优化**: 以全航段需求为基础进行优化决策
3. **运营复杂性降低**: 避免因机型变更导致的运营问题

### 🎯 **目标5: 时区标准化需求预测** (优先级: 🟡 中等)

**预测内容**: 基于UTC标准时间的航班需求分布

**具体说明**:
```
基于UTC时间标准化处理，预测:
- 统一时间基准下的需求分布
- 支持不同时区机场间的航班周转约束

示例输出:
{
  "flight_id": "AA0001",
  "local_departure": "07:55 (UTC-4)",
  "local_arrival": "10:38 (UTC-7)", 
  "utc_departure": "11:55 UTC",
  "utc_arrival": "17:38 UTC",
  "turnover_time_available": 142,  # 分钟
  "time_normalized_demand": 42.7
}
```

**数据依据**:
- 时区转换数据: depoff, arroff的UTC偏移量
- 时间计算特征: 航班时长、周转时间
- 机场运营特征: 各机场的运营窗口限制

**业务价值**:
1. **周转约束验证**: 支持40分钟最小停留时间的约束检查
2. **全球运营标准化**: 统一不同时区的运营时间标准
3. **网络优化**: 支持跨时区航线的整体网络优化

### 🎯 **目标6: 机场级需求汇总预测** (优先级: 🟡 中等)

**预测内容**: 各机场节点的进出港需求汇总

**具体说明**:
```
针对85个机场节点，预测:
- 每个机场的进出港总需求量
- 机场层面的机流平衡约束数据

示例输出:
{
  "airport_code": "TFB",
  "airport_type": "main_hub",
  "outbound_demand": 245.3,
  "inbound_demand": 245.8,
  "net_flow": 0.5,
  "peak_hour_demand": 42.7,
  "required_aircraft_standing": 18,
  "balance_constraint_satisfaction": true
}
```

**数据依据**:
- 机场网络特征: 枢纽度、连接航班数量
- 航线汇聚特征: 各机场的航班频次分布
- 时空需求模式: 不同时间段的机场需求

**业务价值**:
1. **机队平衡约束**: 支持机场进出港飞机流平衡的约束设计
2. **枢纽运营优化**: 指导枢纽机场的机队调配策略
3. **基础设施规划**: 为机场设施容量规划提供依据

### 🎯 **目标7: 机型适配性需求预测** (优先级: 🟡 中等)

**预测内容**: 不同机型在特定航线上的预期需求表现

**具体说明**:
```
针对9种机型在不同航线上的适配性，预测:
- 各机型在对应航线上的座位需求匹配度
- 机型-航线组合的收益和载客率表现

示例输出:
{
  "route": "BER-BOD",
  "market_share": 0.404,
  "aircraft_performance": [
    {
      "aircraft_type": "F8C12Y126",
      "seat_capacity": 146,
      "expected_demand": 142.7,
      "utilization_rate": 0.978,
      "expected_revenue": 15650,
      "suitability_score": 0.92
    },
    {
      "aircraft_type": "F12C0Y132", 
      "seat_capacity": 144,
      "expected_demand": 142.7,
      "utilization_rate": 0.991,
      "expected_revenue": 15420,
      "suitability_score": 0.89
    }
  ],
  "recommendation": "F12C0Y132"
}
```

**数据依据**:
- 机型特征数据: 座位数、成本、舱位配置
- 航线竞争特征: 市场份额、竞争环境
- 历史表现特征: 机型在各航线的历史载客率

**业务价值**:
1. **机型选择支持**: 为优化模型提供机型适配性参考
2. **收益质量提升**: 指导高收益机型的分配策略
3. **机队配置优化**: 为未来机队结构调整提供依据

---

## 📈 预测目标间的关系与集成

### 🔗 预测目标依赖关系

```mermaid
graph TD
    A[预测目标1: 航班总需求] --> B[预测目标2: 分舱位需求]
    A --> C[预测目标3: 预订曲线]
    B --> D[预测目标4: 经停航班需求]
    A --> E[预测目标5: 时区标准化需求]
    A --> F[预测目标6: 机场级需求汇总]
    B --> G[预测目标7: 机型适配性需求]
    
    B --> H[MILP优化模型: 座位容量约束]
    D --> H
    E --> H
    F --> H
    G --> I[优化决策: 机型选择]
```

### 🎯 预测输出到优化模型的接口

#### **核心接口1: 座位容量约束**
```python
def generate_capacity_constraints(predictions):
    """
    生成MILP模型的座位容量约束
    
    基于预测目标1(总需求)和目标2(分舱位需求):
    - Σ(各舱位需求) ≤ 机型总座位数
    - 各舱位需求 ≤ 机型对应舱位座位数
    """
    constraints = []
    for flight_id in all_flights:
        total_demand = predictions[flight_id]['total_demand']
        cabin_demands = predictions[flight_id]['cabin_demands']
        
        for aircraft_type in available_aircraft:
            # 总座位数约束
            total_seats = fleet_data[aircraft_type]['total_seats']
            constraints.append({
                'type': 'total_capacity',
                'flight_id': flight_id,
                'aircraft_type': aircraft_type,
                'constraint': f"{total_demand} <= {total_seats}"
            })
            
            # 分舱位约束
            for cabin_class in cabin_demands:
                cabin_seats = get_cabin_capacity(aircraft_type, cabin_class)
                cabin_demand = cabin_demands[cabin_class]['demand']
                constraints.append({
                    'type': 'cabin_capacity',
                    'flight_id': flight_id,
                    'aircraft_type': aircraft_type,
                    'cabin_class': cabin_class,
                    'constraint': f"{cabin_demand} <= {cabin_seats}"
                })
    
    return constraints
```

#### **核心接口2: 特殊约束处理**
```python
def generate_special_constraints(predictions):
    """
    生成MILP模型的特殊约束
    
    基于预测目标4(经停航班)、目标5(时区)、目标6(机场):
    - 经停航班连续性约束
    - 飞机周转时间约束  
    - 机队平衡约束
    """
    constraints = []
    
    # 经停航班约束 (基于预测目标4)
    for multi_stop_flight in predictions['multi_stop_flights']:
        segments = multi_stop_flight['segments']
        aircraft_constraint = " AND ".join([
            f"aircraft_{segment} = aircraft_{segments[0]}" 
            for segment in segments
        ])
        constraints.append({
            'type': 'multi_stop_continuity',
            'flight_id': multi_stop_flight['flight_id'],
            'constraint': aircraft_constraint
        })
    
    # 时间约束 (基于预测目标5)
    for flight in predictions['time_normalized']:
        constraints.append({
            'type': 'turnover_time',
            'flight_id': flight['flight_id'],
            'constraint': f"turnover_time >= 40"
        })
    
    # 机队平衡约束 (基于预测目标6)
    for airport in predictions['airport_flows']:
        constraints.append({
            'type': 'fleet_balance',
            'airport': airport['code'],
            'constraint': f"inflow_{airport} = outflow_{airport}"
        })
    
    return constraints
```

---

## 📊 预测目标实施优先级与技术路线

### 🎯 实施优先级矩阵

| 预测目标 | 重要性 | 紧急性 | 技术复杂度 | 综合优先级 | 
|----------|--------|--------|------------|------------|
| **目标1: 航班总需求** | 🔴 极高 | 🔴 极高 | 🟡 中等 | 🔴 极高 |
| **目标2: 分舱位需求** | 🔴 极高 | 🔴 极高 | 🔴 高 | 🔴 极高 |
| **目标3: 预订曲线** | 🟡 中等 | 🟡 中等 | 🔴 高 | 🟡 中等 |
| **目标4: 经停航班** | 🟡 中等 | 🟡 中等 | 🟡 中等 | 🟡 中等 |
| **目标5: 时区标准化** | 🟡 中等 | 🟡 中等 | 🟢 低 | 🟡 中等 |
| **目标6: 机场级需求** | 🟡 中等 | 🟢 低 | 🟡 中等 | 🟢 低 |
| **目标7: 机型适配性** | 🟢 低 | 🟢 低 | 🟡 中等 | 🟢 低 |

### 📈 技术实施路线图

#### **第一阶段: 核心需求预测** (1-2周)
```mermaid
gantt
    title 第一阶段: 核心需求预测实施
    dateFormat  YYYY-MM-DD
    section 目标1: 航班总需求
    数据准备        :2025-07-31, 2d
    特征工程        :2025-08-02, 3d
    XGBoost开发     :2025-08-05, 3d
    LSTM/GRU开发    :2025-08-08, 4d
    模型集成        :2025-08-12, 2d
    
    section 目标2: 分舱位需求  
    舱位特征工程    :2025-08-03, 3d
    多任务模型开发  :2025-08-06, 5d
    舱位约束验证    :2025-08-11, 2d
    舱位集成优化    :2025-08-13, 2d
```

#### **第二阶段: 高级预测功能** (1-2周)
```mermaid
gantt
    title 第二阶段: 高级预测功能实施
    dateFormat  YYYY-MM-DD
    section 目标3-5: 时序与特殊处理
    预订曲线建模    :2025-08-14, 3d
    经停航班处理    :2025-08-15, 2d
    时区标准化      :2025-08-16, 2d
    特殊约束集成    :2025-08-18, 3d
    
    section 目标6-7: 系统级预测
    机场网络建模    :2025-08-19, 2d
    机型适配性分析  :2025-08-20, 3d
    系统集成验证    :2025-08-23, 3d
```

#### **第三阶段: 接口与验证** (1周)
```mermaid
gantt
    title 第三阶段: 接口开发与验证
    dateFormat  YYYY-MM-DD
    section 优化模型接口
    约束生成接口    :2025-08-26, 3d
    数据格式转换    :2025-08-28, 2d
    端到端测试      :2025-08-30, 2d
    
    section 性能优化
    推理性能优化    :2025-08-29, 2d
    内存使用优化    :2025-08-29, 2d
    最终验证部署    :2025-08-31, 1d
```

---

## 🎯 预测目标质量要求与验证

### 📊 技术性能指标

| 预测目标 | R²目标 | RMSE目标 | MAE目标 | 验证方法 |
|----------|--------|----------|--------|----------|
| **目标1: 航班总需求** | > 0.85 | < 12% | < 10% | 交叉验证 |
| **目标2: 分舱位需求** | > 0.80 | < 15% | < 12% | 舱位分层验证 |
| **目标3: 预订曲线** | > 0.75 | < 20% | < 18% | 时序验证 |
| **目标4-7** | > 0.70 | < 25% | < 20% | 场景验证 |

### 🎯 业务目标验证

#### **关键业务指标**:
1. **座位容量约束满足率**: 100%
2. **优化模型接口兼容性**: 100%
3. **预测覆盖率**: 100% (所有815个航班)
4. **预测响应时间**: < 5分钟

#### **质量控制流程**:
```python
def validate_prediction_targets(predictions, requirements):
    """
    验证预测目标的质量和完整性
    """
    validation_results = {
        'completeness': check_completeness(predictions),
        'constraint_compatibility': validate_constraints(predictions),
        'business_requirements': validate_business_goals(predictions),
        'technical_accuracy': validate_accuracy_metrics(predictions)
    }
    
    # 质量评分卡
    quality_score = calculate_quality_score(validation_results)
    
    if quality_score >= 0.85:
        return "✅ 通过验证，可用于优化模型"
    elif quality_score >= 0.70:
        return "⚠️ 条件通过，需要优化改进" 
    else:
        return "❌ 验证失败，需要重新训练"
```

---

## 🎯 结论与行动建议

### 📊 核心结论

**需求预测的核心任务是**: **基于航班信息、航线特征、市场份额和历史预订模式，精确预测每个航班的座位需求数量和各舱位等级的座位需求量，为MILP优化模型的座位容量约束提供可靠的数据输入。**

**七个预测目标的层级关系**:
1. **基础层**: 目标1(总需求) + 目标2(分舱位需求) - 核心必须保证
2. **功能层**: 目标3-5 - 支持特殊业务场景
3. **系统层**: 目标6-7 - 提供系统级优化洞察

### 🚀 立即行动项

#### **高优先级行动项**:
1. ✅ **开始目标1实施**: 构建航班总需求预测模型
2. ✅ **并行开发目标2**: 分舱位需求预测的关键算法
3. ✅ **设计优化接口**: 确保预测输出与MILP模型的完美对接

#### **质量控制要点**:
1. 🎯 **精度保证**: 目标1和目标2的R²必须分别达到0.85和0.80以上
2. 🎯 **约束验证**: 所有预测结果必须通过容量约束兼容性检查
3. 🎯 **完整性**: 815个航班的预测覆盖率必须达到100%

### 💡 成功关键要素

#### **数据层面的成功要素**:
- **RD时序特征充分利用**: 31个预订距离点的完整信息提取
- **市场份额有效整合**: 84个机场的竞争环境特征
- **航班模式准确识别**: 经停航班和直飞航班的差异化处理

#### **技术层面的成功要素**:
- **多模型协同策略**: XGBoost、LSTM、GRU的优化组合
- **约束感知预测**: 在预测阶段就考虑优化约束条件
- **接口标准化设计**: 预测输出与优化输入的无缝衔接

---

## 🎯 实施检查清单

### 📋 数据准备检查清单
- [ ] 815个航班的基础特征完整提取
- [ ] 654个航班产品的时序特征正确处理
- [ ] 161个缺失航班的需求估计策略确定
- [ ] 经停航段的识别和配对关系建立
- [ ] 时区转换和UTC时间标准化完成

### 📋 模型开发检查清单  
- [ ] 目标1: 航班总需求预测模型开发完成
- [ ] 目标2: 分舱位需求预测模型开发完成
- [ ] 目标3-7: 高级预测功能按需开发完成
- [ ] 多模型集成和对比验证通过
- [ ] 预测精度达到目标要求

### 📋 接口集成检查清单
- [ ] 座位容量约束生成接口开发完成
- [ ] 特殊约束(经停/时间)处理接口完成
- [ ] 优化模型数据格式转换完成
- [ ] 端到端集成测试验证通过
- [ ] 模型性能和响应时间达标

---

**文档结束**

*本报告明确了需求预测阶段的7个核心目标，为后续的数据预处理、特征工程和模型开发提供了清晰的方向和实施指南。通过这些预测目标的精确实现，将为航空公司机型分配优化项目提供坚实的数据基础。*