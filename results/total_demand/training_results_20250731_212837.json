{"timestamp": "20250731_212837", "model_type": "XGBoost", "prediction_target": "total_demand", "training_results": {"tuning": {"best_params": {"learning_rate": 0.1, "max_depth": 6, "n_estimators": 100, "subsample": 0.8}, "best_score": 0.9108457643257402, "cv_results": {"mean_fit_time": [5.970825592676799, 5.911004463831584, 6.828117211659749, 7.0754445393880205, 6.570951700210571, 7.155113697052002, 8.302407344182333, 8.657496293385824, 6.233471393585205, 5.49724539120992, 7.076756874720256, 7.038631757100423, 6.6934120655059814, 6.960898955663045, 7.386461655298869, 7.485256354014079], "std_fit_time": [0.2792104653967228, 0.29558422106094445, 0.4191742270455307, 0.30021041359551026, 0.2362763797633851, 0.49524401764466847, 0.32156743250300607, 0.2718992826699272, 0.296218298466458, 0.21399523368143022, 0.26640958897492756, 0.09482846731581561, 0.4687278975361481, 0.4878479588321333, 0.16812607504773242, 0.3551956607292514], "mean_score_time": [0.014243046442667643, 0.020709832509358723, 0.0163881778717041, 0.010535637537638346, 0.018287022908528645, 0.016235987345377605, 0.012333869934082031, 0.01317580540974935, 0.020488421122233074, 0.01513814926147461, 0.012823184331258139, 0.017342408498128254, 0.012804428736368815, 0.014935890833536783, 0.01524209976196289, 0.01329652468363444], "std_score_time": [0.004507821565512893, 0.0009838295858768894, 0.0030071962874091986, 0.0005705860324441831, 0.005685945551272022, 0.0061262161288206, 0.0019188231488066348, 0.002542475548725574, 0.0028632288901232496, 0.0016878228418613149, 0.0009739653194797264, 0.0018111181016096545, 0.002167066241422825, 0.002617016404685695, 0.0031820905653179286, 0.0027973777657334872], "param_learning_rate": [0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2], "param_max_depth": [6, 6, 6, 6, 8, 8, 8, 8, 6, 6, 6, 6, 8, 8, 8, 8], "param_n_estimators": [100, 100, 200, 200, 100, 100, 200, 200, 100, 100, 200, 200, 100, 100, 200, 200], "param_subsample": [0.8, 1.0, 0.8, 1.0, 0.8, 1.0, 0.8, 1.0, 0.8, 1.0, 0.8, 1.0, 0.8, 1.0, 0.8, 1.0], "params": [{"learning_rate": 0.1, "max_depth": 6, "n_estimators": 100, "subsample": 0.8}, {"learning_rate": 0.1, "max_depth": 6, "n_estimators": 100, "subsample": 1.0}, {"learning_rate": 0.1, "max_depth": 6, "n_estimators": 200, "subsample": 0.8}, {"learning_rate": 0.1, "max_depth": 6, "n_estimators": 200, "subsample": 1.0}, {"learning_rate": 0.1, "max_depth": 8, "n_estimators": 100, "subsample": 0.8}, {"learning_rate": 0.1, "max_depth": 8, "n_estimators": 100, "subsample": 1.0}, {"learning_rate": 0.1, "max_depth": 8, "n_estimators": 200, "subsample": 0.8}, {"learning_rate": 0.1, "max_depth": 8, "n_estimators": 200, "subsample": 1.0}, {"learning_rate": 0.2, "max_depth": 6, "n_estimators": 100, "subsample": 0.8}, {"learning_rate": 0.2, "max_depth": 6, "n_estimators": 100, "subsample": 1.0}, {"learning_rate": 0.2, "max_depth": 6, "n_estimators": 200, "subsample": 0.8}, {"learning_rate": 0.2, "max_depth": 6, "n_estimators": 200, "subsample": 1.0}, {"learning_rate": 0.2, "max_depth": 8, "n_estimators": 100, "subsample": 0.8}, {"learning_rate": 0.2, "max_depth": 8, "n_estimators": 100, "subsample": 1.0}, {"learning_rate": 0.2, "max_depth": 8, "n_estimators": 200, "subsample": 0.8}, {"learning_rate": 0.2, "max_depth": 8, "n_estimators": 200, "subsample": 1.0}], "split0_test_score": [0.9168428210927277, 0.9189267261373351, 0.9169298738459544, 0.9190486607207256, 0.9175718703767968, 0.9157689774978203, 0.9175638883393471, 0.9158225370391718, 0.9189733808171425, 0.9200745837271256, 0.9189867270989943, 0.9201622429487171, 0.9069096180365251, 0.9153561429314124, 0.9069081193678031, 0.9153558678033148], "split1_test_score": [0.89155762929625, 0.8874085029428493, 0.8916079611181686, 0.8870325352899803, 0.8937412681298614, 0.883063636329654, 0.8936734176520437, 0.8830142503236802, 0.8965651722945499, 0.887216332677068, 0.8964880157168198, 0.8872683067719025, 0.893510308614855, 0.8828230043624482, 0.8935058450716238, 0.882822083794351], "split2_test_score": [0.9241368425882427, 0.8959433423432707, 0.9239214583990503, 0.895488375846969, 0.9110076188961427, 0.8929084119970774, 0.9109148513084249, 0.8929019803068976, 0.9069457330375267, 0.9054588074209972, 0.9069501403572824, 0.9053873788144451, 0.9133602544881372, 0.896147120894923, 0.913358130752056, 0.8961451374738234], "mean_test_score": [0.9108457643257402, 0.9007595238078183, 0.9108197644543911, 0.9005231906192249, 0.9074402524676003, 0.897247008608184, 0.9073840524332718, 0.8972462558899165, 0.9074947620497397, 0.9042499079417302, 0.9074749610576989, 0.9042726428450215, 0.9045933937131725, 0.8981087560629278, 0.9045906983971609, 0.8981076963571631], "std_test_score": [0.013960057356165394, 0.013310304310544366, 0.01388141693567431, 0.013546711574954231, 0.01005050507153839, 0.013699815099651682, 0.010067722448094946, 0.013741675469349604, 0.009156346685594164, 0.013441533741365013, 0.009192554271085793, 0.013452006950940633, 0.008267557443813167, 0.013353833049880526, 0.008268661385043015, 0.013354162984213536], "rank_test_score": [1, 11, 2, 12, 5, 15, 6, 16, 3, 10, 4, 9, 7, 13, 8, 14]}}, "training_complete": true, "cv_mean_r2": 0.9198545252216389, "cv_std_r2": 0.00892219475997994, "feature_importance": {"feature": {"79": "rd_bookings_std", "62": "class_T_peak", "75": "class_Y_std", "15": "class_B_std", "74": "class_Y_peak", "50": "class_Q_peak", "89": "time_offset_magnitude", "30": "class_H_long_ratio", "86": "hub_connection_score", "96": "competition_level_encoded", "51": "class_Q_std", "39": "class_L_std", "78": "class_Y_long_ratio", "73": "class_Y_total", "53": "class_Q_medium_ratio", "65": "class_T_medium_ratio", "56": "class_S_peak", "5": "dep_offset", "40": "class_L_early_ratio", "60": "class_S_long_ratio", "46": "class_M_early_ratio", "61": "class_T_total", "36": "class_K_long_ratio", "28": "class_H_early_ratio", "10": "is_hub_to_hub", "4": "flight_duration", "72": "class_V_long_ratio", "69": "class_V_std", "6": "arr_offset", "38": "class_L_peak", "54": "class_Q_long_ratio", "66": "class_T_long_ratio", "59": "class_S_medium_ratio", "71": "class_V_medium_ratio", "44": "class_M_peak", "17": "class_B_medium_ratio", "23": "class_G_medium_ratio", "25": "class_H_total", "7": "market_share", "52": "class_Q_early_ratio", "14": "class_B_peak", "22": "class_G_early_ratio", "20": "class_G_peak", "77": "class_Y_medium_ratio", "8": "is_hub_origin", "41": "class_L_medium_ratio", "35": "class_K_medium_ratio", "70": "class_V_early_ratio", "68": "class_V_peak", "100": "route_encoded", "19": "class_G_total", "90": "route_frequency", "45": "class_M_std", "76": "class_Y_early_ratio", "49": "class_Q_total", "63": "class_T_std", "24": "class_G_long_ratio", "26": "class_H_peak", "1": "arrival_time", "97": "competition_level_freq", "67": "class_V_total", "57": "class_S_std", "32": "class_K_peak", "58": "class_S_early_ratio", "33": "class_K_std", "92": "departure_period_encoded", "64": "class_T_early_ratio", "13": "class_B_total", "37": "class_L_total", "29": "class_H_medium_ratio", "55": "class_S_total", "34": "class_K_early_ratio", "0": "departure_time", "48": "class_M_long_ratio", "31": "class_K_total", "94": "arrival_period_encoded", "3": "arrival_hour", "21": "class_G_std", "42": "class_L_long_ratio", "43": "class_M_total", "9": "is_hub_destination", "18": "class_B_long_ratio", "16": "class_B_early_ratio", "93": "departure_period_freq", "47": "class_M_medium_ratio", "27": "class_H_std", "87": "seat_capacity", "88": "capacity_ratio", "2": "departure_hour", "81": "avg_fleet_seats", "82": "max_fleet_seats", "83": "min_fleet_seats", "84": "fleet_aircraft_types", "85": "competition_score", "91": "seats_per_hour", "95": "arrival_period_freq", "80": "has_historical_data", "12": "is_multi_stop", "11": "same_airport", "98": "flight_type_encoded", "99": "flight_type_freq", "101": "route_freq"}, "importance": {"79": 0.5037176609039307, "62": 0.028201736509799957, "75": 0.02728121541440487, "15": 0.020009856671094894, "74": 0.015058042481541634, "50": 0.01458905078470707, "89": 0.012617678381502628, "30": 0.012389130890369415, "86": 0.011547527275979519, "96": 0.010903722606599331, "51": 0.010865917429327965, "39": 0.010692195035517216, "78": 0.010563190095126629, "73": 0.010342378169298172, "53": 0.009214813821017742, "65": 0.008991105481982231, "56": 0.008041741326451302, "5": 0.007316156756132841, "40": 0.007123712450265884, "60": 0.006915106903761625, "46": 0.006899170111864805, "61": 0.006878392305225134, "36": 0.00680951913818717, "28": 0.006719101220369339, "10": 0.006708258297294378, "4": 0.006502918899059296, "72": 0.006462360732257366, "69": 0.006174333393573761, "6": 0.005937478505074978, "38": 0.0056658401153981686, "54": 0.005535638425499201, "66": 0.005441385321319103, "59": 0.0054399119690060616, "71": 0.005436082370579243, "44": 0.005414716433733702, "17": 0.0053842077031731606, "23": 0.005200384650379419, "25": 0.005185318179428577, "7": 0.004749056417495012, "52": 0.0046832263469696045, "14": 0.004615883808583021, "22": 0.0044741034507751465, "20": 0.004364029970020056, "77": 0.004257466644048691, "8": 0.0042438749223947525, "41": 0.0041122534312307835, "35": 0.0040978635661304, "70": 0.004058996215462685, "68": 0.003999078180640936, "100": 0.00398793863132596, "19": 0.003953660372644663, "90": 0.0039534433744847775, "45": 0.0038783119525760412, "76": 0.003839784534648061, "49": 0.003732226323336363, "63": 0.0036711355205625296, "24": 0.003573061665520072, "26": 0.0034658319782465696, "1": 0.0033929438795894384, "97": 0.0033688757102936506, "67": 0.0032970753964036703, "57": 0.003285263432189822, "32": 0.0032812177669256926, "58": 0.003098088316619396, "33": 0.0029965557623654604, "92": 0.0029765977524220943, "64": 0.002932077506557107, "13": 0.002913098782300949, "37": 0.0028650574386119843, "29": 0.002752413274720311, "55": 0.00269122701138258, "34": 0.0023736879229545593, "0": 0.002216063905507326, "48": 0.001989391865208745, "31": 0.0019377295393496752, "94": 0.0018710433505475521, "3": 0.001864961232058704, "21": 0.0016711850184947252, "42": 0.0015121570322662592, "43": 0.0014467771397903562, "9": 0.0014348655240610242, "18": 0.001397599815391004, "16": 0.0013062425423413515, "93": 0.0012208845000714064, "47": 0.0011066504521295428, "27": 0.0009120005997829139, "87": 0.0, "88": 0.0, "2": 0.0, "81": 0.0, "82": 0.0, "83": 0.0, "84": 0.0, "85": 0.0, "91": 0.0, "95": 0.0, "80": 0.0, "12": 0.0, "11": 0.0, "98": 0.0, "99": 0.0, "101": 0.0}}, "test_metrics": {"r2_score": 0.910251003181532, "mse": 348.5852176949915, "mae": 12.841261763894488, "rmse": 18.6704369979653, "mape": 21.02098875331679}}, "model_path": "/home/<USER>/cem208/code/ie2025/results/total_demand/xgboost_total_demand_model_20250731_212837.joblib"}