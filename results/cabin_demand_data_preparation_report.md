# 分舱位需求预测数据准备报告

## 概述

共处理 11 个舱位等级: B, G, H, K, L, M, Q, S, T, V, Y

### 舱位 B

- **数据规模**: 652 训练 + 163 测试
- **特征数量**: 33
- **目标变量**: class_B_total
- **需求均值**: 1.32
- **需求范围**: 0.00 - 48.68
- **有需求航班**: 370 (45.4%)

### 舱位 G

- **数据规模**: 652 训练 + 163 测试
- **特征数量**: 33
- **目标变量**: class_G_total
- **需求均值**: 7.59
- **需求范围**: 0.00 - 123.05
- **有需求航班**: 622 (76.3%)

### 舱位 H

- **数据规模**: 652 训练 + 163 测试
- **特征数量**: 33
- **目标变量**: class_H_total
- **需求均值**: 2.19
- **需求范围**: 0.00 - 32.09
- **有需求航班**: 499 (61.2%)

### 舱位 K

- **数据规模**: 652 训练 + 163 测试
- **特征数量**: 33
- **目标变量**: class_K_total
- **需求均值**: 9.00
- **需求范围**: 0.00 - 158.15
- **有需求航班**: 586 (71.9%)

### 舱位 L

- **数据规模**: 652 训练 + 163 测试
- **特征数量**: 33
- **目标变量**: class_L_total
- **需求均值**: 4.83
- **需求范围**: 0.00 - 100.37
- **有需求航班**: 540 (66.3%)

### 舱位 M

- **数据规模**: 652 训练 + 163 测试
- **特征数量**: 33
- **目标变量**: class_M_total
- **需求均值**: 1.63
- **需求范围**: 0.00 - 20.21
- **有需求航班**: 404 (49.6%)

### 舱位 Q

- **数据规模**: 652 训练 + 163 测试
- **特征数量**: 33
- **目标变量**: class_Q_total
- **需求均值**: 2.78
- **需求范围**: 0.00 - 34.41
- **有需求航班**: 507 (62.2%)

### 舱位 S

- **数据规模**: 652 训练 + 163 测试
- **特征数量**: 33
- **目标变量**: class_S_total
- **需求均值**: 0.61
- **需求范围**: 0.00 - 32.28
- **有需求航班**: 262 (32.1%)

### 舱位 T

- **数据规模**: 652 训练 + 163 测试
- **特征数量**: 33
- **目标变量**: class_T_total
- **需求均值**: 11.90
- **需求范围**: 0.00 - 130.21
- **有需求航班**: 635 (77.9%)

### 舱位 V

- **数据规模**: 652 训练 + 163 测试
- **特征数量**: 33
- **目标变量**: class_V_total
- **需求均值**: 4.27
- **需求范围**: 0.00 - 94.56
- **有需求航班**: 551 (67.6%)

### 舱位 Y

- **数据规模**: 652 训练 + 163 测试
- **特征数量**: 33
- **目标变量**: class_Y_total
- **需求均值**: 3.07
- **需求范围**: 0.00 - 80.37
- **有需求航班**: 568 (69.7%)

## 总体统计

- **总舱位数**: 11
- **活跃舱位数**: 11 (有数据)
- **总样本数**: 8965
- **平均每舱位特征数**: 33

## 舱位需求分析

- **T舱位**: 均值 11.9, 活跃航班 635 (77.9%)
- **K舱位**: 均值 9.0, 活跃航班 586 (71.9%)
- **G舱位**: 均值 7.6, 活跃航班 622 (76.3%)
- **L舱位**: 均值 4.8, 活跃航班 540 (66.3%)
- **V舱位**: 均值 4.3, 活跃航班 551 (67.6%)
- **Y舱位**: 均值 3.1, 活跃航班 568 (69.7%)
- **Q舱位**: 均值 2.8, 活跃航班 507 (62.2%)
- **H舱位**: 均值 2.2, 活跃航班 499 (61.2%)
- **M舱位**: 均值 1.6, 活跃航班 404 (49.6%)
- **B舱位**: 均值 1.3, 活跃航班 370 (45.4%)
- **S舱位**: 均值 0.6, 活跃航班 262 (32.1%)

## 数据质量评估

### 数据质量良好，未发现明显问题

## 使用建议

1. **高需求舱位**: T, K, G 等模式识别和预测效果好
2. **中等需求舱位**: L, V, Y 需要考虑稀疏数据处理
3. **低需求舱位**:  建议使用专门的小样本学习方法

## 文件结构

```
data/processed/cabin_demand/

├── B/

│   ├── B_X_train.csv     # 训练特征

│   ├── B_X_test.csv      # 测试特征

│   ├── B_y_train.csv     # 训练目标

│   ├── B_y_test.csv      # 测试目标

│   ├── B_features.csv    # 特征列表

│   ├── B_dataset_info.json # 数据集信息

│   ├── B_scaler.pkl       # 标准化器

│   └── B_label_encoders.pkl # 编码器

├── G/

│   ├── G_X_train.csv     # 训练特征

│   ├── G_X_test.csv      # 测试特征

│   ├── G_y_train.csv     # 训练目标

│   ├── G_y_test.csv      # 测试目标

│   ├── G_features.csv    # 特征列表

│   ├── G_dataset_info.json # 数据集信息

│   ├── G_scaler.pkl       # 标准化器

│   └── G_label_encoders.pkl # 编码器

├── H/

│   ├── H_X_train.csv     # 训练特征

│   ├── H_X_test.csv      # 测试特征

│   ├── H_y_train.csv     # 训练目标

│   ├── H_y_test.csv      # 测试目标

│   ├── H_features.csv    # 特征列表

│   ├── H_dataset_info.json # 数据集信息

│   ├── H_scaler.pkl       # 标准化器

│   └── H_label_encoders.pkl # 编码器

├── K/

│   ├── K_X_train.csv     # 训练特征

│   ├── K_X_test.csv      # 测试特征

│   ├── K_y_train.csv     # 训练目标

│   ├── K_y_test.csv      # 测试目标

│   ├── K_features.csv    # 特征列表

│   ├── K_dataset_info.json # 数据集信息

│   ├── K_scaler.pkl       # 标准化器

│   └── K_label_encoders.pkl # 编码器

├── L/

│   ├── L_X_train.csv     # 训练特征

│   ├── L_X_test.csv      # 测试特征

│   ├── L_y_train.csv     # 训练目标

│   ├── L_y_test.csv      # 测试目标

│   ├── L_features.csv    # 特征列表

│   ├── L_dataset_info.json # 数据集信息

│   ├── L_scaler.pkl       # 标准化器

│   └── L_label_encoders.pkl # 编码器

├── M/

│   ├── M_X_train.csv     # 训练特征

│   ├── M_X_test.csv      # 测试特征

│   ├── M_y_train.csv     # 训练目标

│   ├── M_y_test.csv      # 测试目标

│   ├── M_features.csv    # 特征列表

│   ├── M_dataset_info.json # 数据集信息

│   ├── M_scaler.pkl       # 标准化器

│   └── M_label_encoders.pkl # 编码器

├── Q/

│   ├── Q_X_train.csv     # 训练特征

│   ├── Q_X_test.csv      # 测试特征

│   ├── Q_y_train.csv     # 训练目标

│   ├── Q_y_test.csv      # 测试目标

│   ├── Q_features.csv    # 特征列表

│   ├── Q_dataset_info.json # 数据集信息

│   ├── Q_scaler.pkl       # 标准化器

│   └── Q_label_encoders.pkl # 编码器

├── S/

│   ├── S_X_train.csv     # 训练特征

│   ├── S_X_test.csv      # 测试特征

│   ├── S_y_train.csv     # 训练目标

│   ├── S_y_test.csv      # 测试目标

│   ├── S_features.csv    # 特征列表

│   ├── S_dataset_info.json # 数据集信息

│   ├── S_scaler.pkl       # 标准化器

│   └── S_label_encoders.pkl # 编码器

├── T/

│   ├── T_X_train.csv     # 训练特征

│   ├── T_X_test.csv      # 测试特征

│   ├── T_y_train.csv     # 训练目标

│   ├── T_y_test.csv      # 测试目标

│   ├── T_features.csv    # 特征列表

│   ├── T_dataset_info.json # 数据集信息

│   ├── T_scaler.pkl       # 标准化器

│   └── T_label_encoders.pkl # 编码器

├── V/

│   ├── V_X_train.csv     # 训练特征

│   ├── V_X_test.csv      # 测试特征

│   ├── V_y_train.csv     # 训练目标

│   ├── V_y_test.csv      # 测试目标

│   ├── V_features.csv    # 特征列表

│   ├── V_dataset_info.json # 数据集信息

│   ├── V_scaler.pkl       # 标准化器

│   └── V_label_encoders.pkl # 编码器

├── Y/

│   ├── Y_X_train.csv     # 训练特征

│   ├── Y_X_test.csv      # 测试特征

│   ├── Y_y_train.csv     # 训练目标

│   ├── Y_y_test.csv      # 测试目标

│   ├── Y_features.csv    # 特征列表

│   ├── Y_dataset_info.json # 数据集信息

│   ├── Y_scaler.pkl       # 标准化器

│   └── Y_label_encoders.pkl # 编码器

└── cabin_demand_summary.json           # 总体汇总

```

---

*生成时间: 2025-07-31 20:19:14*
*作者: Claude for IE2025项目*