# 预订曲线预测数据准备报告

## 概述

预订曲线预测基于原始RD时序数据，构建了4种不同类型的预测任务：

### FULL_CURVE - 预测全部33个RD时间点的预订量

- **输入特征**: 12 维
- **输出维度**: 33 维
- **训练样本**: 37752
- **测试样本**: 9438
- **时间点数**: 31

### WINDOW_AGGREGATE - 预测早期、中期、长期三个时间窗口的累计预订量

- **输入特征**: 12 维
- **输出维度**: 3 维
- **训练样本**: 37752
- **测试样本**: 9438
- **时间窗口**: early, medium, long

### KEY_POINTS - 预测6个关键时间点的预订量

- **输入特征**: 12 维
- **输出维度**: 6 维
- **训练样本**: 37752
- **测试样本**: 9438
- **关键时间点**: RD0, RD7, RD14, RD21, RD28, RD33

### FINAL_DEMAND - 基于早期预订数据预测最终(RD99)预订量

- **输入特征**: 17 维
- **输出维度**: 1 维
- **训练样本**: 37752
- **测试样本**: 9438
- **输入RD**: RD0, RD1, RD2, RD4, RD6
- **预测目标**: RD99

## 数据源分析

- **原始产品数据**: 47,190 条记录
- **舱位等级**: 11 个
- **RD时间点**: 31 个
- **时间跨度**: RD0 到 RD330 (提前0天到330天)

## 总体统计

- **预测任务数**: 4
- **总样本数**: 188,760
- **平均特征数**: 13

## 预测任务适用场景

### FULL_CURVE

适合需要完整预订曲线的场景，如收益管理系统的动态定价

### WINDOW_AGGREGATE

适合需要预测预订进度的场景，如运力调配决策

### KEY_POINTS

适合关注特定时间点的场景，如营销活动效果评估

### FINAL_DEMAND

适合基于早期数据预测最终需求的场景，如初步运力规划

## 技术特点

- **数据标准化**: 使用Z-score标准化处理数值特征

- **一致性分割**: 所有数据集使用相同的随机种子(42)确保一致性

- **特征工程**: 综合静态特征和动态特征

- **多任务学习**: 支持不同粒度的预测任务

## 文件结构

```
data/processed/booking_curve/

├── final_demand/

│   ├── final_demand_X_train.npy     # 训练特征

│   ├── final_demand_X_test.npy      # 测试特征

│   ├── final_demand_y_train.npy     # 训练目标

│   ├── final_demand_y_test.npy      # 测试目标

│   ├── final_demand_scaler.pkl      # 标准化器

│   └── final_demand_dataset_info.json # 数据集信息

├── full_curve/

│   ├── full_curve_X_train.npy     # 训练特征

│   ├── full_curve_X_test.npy      # 测试特征

│   ├── full_curve_y_train.npy     # 训练目标

│   ├── full_curve_y_test.npy      # 测试目标

│   ├── full_curve_scaler.pkl      # 标准化器

│   └── full_curve_dataset_info.json # 数据集信息

├── key_points/

│   ├── key_points_X_train.npy     # 训练特征

│   ├── key_points_X_test.npy      # 测试特征

│   ├── key_points_y_train.npy     # 训练目标

│   ├── key_points_y_test.npy      # 测试目标

│   ├── key_points_scaler.pkl      # 标准化器

│   └── key_points_dataset_info.json # 数据集信息

├── window_aggregate/

│   ├── window_aggregate_X_train.npy     # 训练特征

│   ├── window_aggregate_X_test.npy      # 测试特征

│   ├── window_aggregate_y_train.npy     # 训练目标

│   ├── window_aggregate_y_test.npy      # 测试目标

│   ├── window_aggregate_scaler.pkl      # 标准化器

│   └── window_aggregate_dataset_info.json # 数据集信息

└── booking_curve_summary.json           # 总体汇总

```

---

*生成时间: 2025-07-31 20:36:46*
*作者: Claude for IE2025项目*