{"总需求预测": {"dataset": "总需求预测", "checks": {"file_integrity": {"status": "✅", "details": "完整文件: 8/8"}, "data_dimensions": {"status": "✅", "details": "训练集: (652, 102), 测试集: (163, 102)"}, "feature_consistency": {"status": "✅", "details": "训练特征数: 102, 测试特征数: 102"}, "missing_values": {"status": "✅", "details": "训练缺失: 0, 测试缺失: 0"}, "data_range": {"status": "✅", "details": "需求范围: [0.00, 284.24]"}, "split_ratio": {"status": "✅", "details": "训练比例: 0.80 (期望: 0.8)"}}}, "分舱位需求预测": {"dataset": "分舱位需求预测", "cabin_results": {"B": {"cabin": "B", "checks": {"file_integrity": {"status": "✅", "details": "完整文件: 8/8"}, "dimension_consistency": {"status": "✅", "details": "特征数: 33"}, "data_quality": {"status": "✅", "details": "训练/测试缺失: 0/0"}, "demand_statistics": {"status": "✅", "details": "均值: 1.32, 正比例: 45.4%"}}}, "G": {"cabin": "G", "checks": {"file_integrity": {"status": "✅", "details": "完整文件: 8/8"}, "dimension_consistency": {"status": "✅", "details": "特征数: 33"}, "data_quality": {"status": "✅", "details": "训练/测试缺失: 0/0"}, "demand_statistics": {"status": "✅", "details": "均值: 7.59, 正比例: 76.4%"}}}, "H": {"cabin": "H", "checks": {"file_integrity": {"status": "✅", "details": "完整文件: 8/8"}, "dimension_consistency": {"status": "✅", "details": "特征数: 33"}, "data_quality": {"status": "✅", "details": "训练/测试缺失: 0/0"}, "demand_statistics": {"status": "✅", "details": "均值: 2.19, 正比例: 61.2%"}}}, "K": {"cabin": "K", "checks": {"file_integrity": {"status": "✅", "details": "完整文件: 8/8"}, "dimension_consistency": {"status": "✅", "details": "特征数: 33"}, "data_quality": {"status": "✅", "details": "训练/测试缺失: 0/0"}, "demand_statistics": {"status": "✅", "details": "均值: 9.00, 正比例: 71.9%"}}}, "L": {"cabin": "L", "checks": {"file_integrity": {"status": "✅", "details": "完整文件: 8/8"}, "dimension_consistency": {"status": "✅", "details": "特征数: 33"}, "data_quality": {"status": "✅", "details": "训练/测试缺失: 0/0"}, "demand_statistics": {"status": "✅", "details": "均值: 4.83, 正比例: 66.3%"}}}, "M": {"cabin": "M", "checks": {"file_integrity": {"status": "✅", "details": "完整文件: 8/8"}, "dimension_consistency": {"status": "✅", "details": "特征数: 33"}, "data_quality": {"status": "✅", "details": "训练/测试缺失: 0/0"}, "demand_statistics": {"status": "✅", "details": "均值: 1.63, 正比例: 49.5%"}}}, "Q": {"cabin": "Q", "checks": {"file_integrity": {"status": "✅", "details": "完整文件: 8/8"}, "dimension_consistency": {"status": "✅", "details": "特征数: 33"}, "data_quality": {"status": "✅", "details": "训练/测试缺失: 0/0"}, "demand_statistics": {"status": "✅", "details": "均值: 2.78, 正比例: 62.1%"}}}, "S": {"cabin": "S", "checks": {"file_integrity": {"status": "✅", "details": "完整文件: 8/8"}, "dimension_consistency": {"status": "✅", "details": "特征数: 33"}, "data_quality": {"status": "✅", "details": "训练/测试缺失: 0/0"}, "demand_statistics": {"status": "✅", "details": "均值: 0.61, 正比例: 32.2%"}}}, "T": {"cabin": "T", "checks": {"file_integrity": {"status": "✅", "details": "完整文件: 8/8"}, "dimension_consistency": {"status": "✅", "details": "特征数: 33"}, "data_quality": {"status": "✅", "details": "训练/测试缺失: 0/0"}, "demand_statistics": {"status": "✅", "details": "均值: 11.90, 正比例: 77.9%"}}}, "V": {"cabin": "V", "checks": {"file_integrity": {"status": "✅", "details": "完整文件: 8/8"}, "dimension_consistency": {"status": "✅", "details": "特征数: 33"}, "data_quality": {"status": "✅", "details": "训练/测试缺失: 0/0"}, "demand_statistics": {"status": "✅", "details": "均值: 4.27, 正比例: 67.6%"}}}, "Y": {"cabin": "Y", "checks": {"file_integrity": {"status": "✅", "details": "完整文件: 8/8"}, "dimension_consistency": {"status": "✅", "details": "特征数: 33"}, "data_quality": {"status": "✅", "details": "训练/测试缺失: 0/0"}, "demand_statistics": {"status": "✅", "details": "均值: 3.07, 正比例: 69.6%"}}}}, "overall_checks": {"dataset_completeness": {"status": "✅", "details": "完整舱位: 11/11"}, "file_coverage": {"status": "✅", "details": "总文件存在率: 100.0%"}}}, "预订曲线预测": {"dataset": "预订曲线预测", "type_results": {"full_curve": {"type": "full_curve", "checks": {"file_integrity": {"status": "✅", "details": "完整文件: 6/6"}, "dataset_info": {"status": "✅", "details": "输入: 12维, 输出: 33维"}, "sample_consistency": {"status": "✅", "details": "训练: 37752样本, 测试: 9438样本"}, "feature_consistency": {"status": "✅", "details": "特征维度: 12"}}}, "window_aggregate": {"type": "window_aggregate", "checks": {"file_integrity": {"status": "✅", "details": "完整文件: 6/6"}, "dataset_info": {"status": "✅", "details": "输入: 12维, 输出: 3维"}, "sample_consistency": {"status": "✅", "details": "训练: 37752样本, 测试: 9438样本"}, "feature_consistency": {"status": "✅", "details": "特征维度: 12"}}}, "key_points": {"type": "key_points", "checks": {"file_integrity": {"status": "✅", "details": "完整文件: 6/6"}, "dataset_info": {"status": "✅", "details": "输入: 12维, 输出: 6维"}, "sample_consistency": {"status": "✅", "details": "训练: 37752样本, 测试: 9438样本"}, "feature_consistency": {"status": "✅", "details": "特征维度: 12"}}}, "final_demand": {"type": "final_demand", "checks": {"file_integrity": {"status": "✅", "details": "完整文件: 6/6"}, "dataset_info": {"status": "✅", "details": "输入: 17维, 输出: 1维"}, "sample_consistency": {"status": "✅", "details": "训练: 37752样本, 测试: 9438样本"}, "feature_consistency": {"status": "✅", "details": "特征维度: 17"}}}}, "overall_checks": {"dataset_completeness": {"status": "✅", "details": "完整数据集: 4/4"}, "file_coverage": {"status": "✅", "details": "总文件存在率: 100.0%"}}}, "交叉验证": {"dataset": "交叉验证", "checks": {"sample_consisistency": {"status": "✅", "details": "分舱位样本: 815 (全部一致: True)"}, "total_vs_cabin": {"status": "⚠️", "details": "总需求: 0, 单舱位: 815"}, "booking_sample_size": {"status": "✅", "details": "预订曲线样本: 47,190"}}}}