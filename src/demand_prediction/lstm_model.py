"""
LSTM/GRU Model for Demand Prediction
Implements deep learning models for time series demand forecasting
"""
import pandas as pd
import numpy as np
import os

# Try to configure TensorFlow to avoid CUDA issues
try:
    # Disable GPU if there are CUDA compatibility issues
    os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import LSTM, GRU, Dense, Dropout
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
    TENSORFLOW_AVAILABLE = True
    print("TensorFlow loaded successfully (CPU mode)")
except ImportError:
    TENSORFLOW_AVAILABLE = False
    print("TensorFlow not available, LSTM/GRU models will not work")
except Exception as e:
    # If CUDA issues occur, try CPU-only mode
    try:
        os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
        import tensorflow as tf
        from tensorflow.keras.models import Sequential
        from tensorflow.keras.layers import <PERSON><PERSON><PERSON>, GR<PERSON>, <PERSON><PERSON>, Dropout
        from tensorflow.keras.optimizers import <PERSON>
        from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
        TENSORFLOW_AVAILABLE = True
        print("TensorFlow loaded successfully (CPU mode due to CUDA issues)")
    except Exception as e2:
        TENSORFLOW_AVAILABLE = False
        print(f"TensorFlow not available due to: {e2}")

from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
import joblib
import logging
from typing import Dict, Tuple, Optional

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LSTMDemandPredictor:
    """
    LSTM/GRU model for airline demand prediction
    """
    
    def __init__(self, model_type: str = 'LSTM'):
        """
        Initialize LSTM/GRU model
        
        Args:
            model_type (str): 'LSTM' or 'GRU'
        """
        if not TENSORFLOW_AVAILABLE:
            raise ImportError("TensorFlow is required for LSTM/GRU models")
            
        self.model_type = model_type.upper()
        self.model = None
        self.is_trained = False
        self.history = None
        self.sequence_length = 10  # Default sequence length
        
    def prepare_sequences(self, data: pd.DataFrame, target: pd.Series, sequence_length: int = 10) -> Tuple[np.ndarray, np.ndarray]:
        """
        Prepare sequential data for LSTM/GRU training
        
        Args:
            data (pd.DataFrame): Feature data
            target (pd.Series): Target variable
            sequence_length (int): Length of sequences
            
        Returns:
            Tuple: (X_sequences, y_sequences)
        """
        logger.info(f"Preparing sequences with length {sequence_length}")
        
        # Convert to numpy arrays
        features = data.values
        targets = target.values
        
        X_seq, y_seq = [], []
        
        for i in range(sequence_length, len(features)):
            X_seq.append(features[i-sequence_length:i])
            y_seq.append(targets[i])
            
        X_seq = np.array(X_seq)
        y_seq = np.array(y_seq)
        
        logger.info(f"Created {len(X_seq)} sequences of shape {X_seq.shape}")
        return X_seq, y_seq
    
    def build_model(self, input_shape: Tuple, **kwargs) -> tf.keras.Model:
        """
        Build LSTM/GRU model architecture
        
        Args:
            input_shape (Tuple): Shape of input data (timesteps, features)
            **kwargs: Additional model parameters
            
        Returns:
            tf.keras.Model: Built model
        """
        logger.info(f"Building {self.model_type} model with input shape {input_shape}")
        
        # Default parameters
        default_params = {
            'lstm_units': [50, 50],
            'dropout_rate': 0.2,
            'dense_units': [50, 25],
            'learning_rate': 0.001
        }
        
        # Update with custom parameters
        default_params.update(kwargs)
        
        # Create model
        model = Sequential()
        
        # Add first layer
        if self.model_type == 'LSTM':
            model.add(LSTM(default_params['lstm_units'][0], 
                          return_sequences=len(default_params['lstm_units']) > 1,
                          input_shape=input_shape))
        else:  # GRU
            model.add(GRU(default_params['lstm_units'][0], 
                         return_sequences=len(default_params['lstm_units']) > 1,
                         input_shape=input_shape))
        
        model.add(Dropout(default_params['dropout_rate']))
        
        # Add additional layers
        for i in range(1, len(default_params['lstm_units'])):
            return_seq = (i < len(default_params['lstm_units']) - 1)
            if self.model_type == 'LSTM':
                model.add(LSTM(default_params['lstm_units'][i], return_sequences=return_seq))
            else:  # GRU
                model.add(GRU(default_params['lstm_units'][i], return_sequences=return_seq))
            model.add(Dropout(default_params['dropout_rate']))
        
        # Add dense layers
        for units in default_params['dense_units']:
            model.add(Dense(units, activation='relu'))
            model.add(Dropout(default_params['dropout_rate']))
        
        # Output layer
        model.add(Dense(1))
        
        # Compile model
        optimizer = Adam(learning_rate=default_params['learning_rate'])
        model.compile(optimizer=optimizer, loss='mse', metrics=['mae'])
        
        logger.info(f"{self.model_type} model built successfully")
        return model
    
    def train(self, X_train: np.ndarray, y_train: np.ndarray, 
              X_val: Optional[np.ndarray] = None, y_val: Optional[np.ndarray] = None,
              epochs: int = 100, batch_size: int = 32, **kwargs) -> Dict:
        """
        Train the LSTM/GRU model
        
        Args:
            X_train (np.ndarray): Training sequences
            y_train (np.ndarray): Training targets
            X_val (np.ndarray): Validation sequences (optional)
            y_val (np.ndarray): Validation targets (optional)
            epochs (int): Number of training epochs
            batch_size (int): Batch size
            **kwargs: Additional training parameters
            
        Returns:
            Dict: Training results and history
        """
        logger.info(f"Training {self.model_type} model for {epochs} epochs")
        
        # Default callbacks
        callbacks = [
            EarlyStopping(patience=10, restore_best_weights=True),
            ReduceLROnPlateau(patience=5, factor=0.5, min_lr=1e-7)
        ]
        
        # Validation data
        validation_data = (X_val, y_val) if X_val is not None and y_val is not None else None
        
        # Train model
        self.history = self.model.fit(
            X_train, y_train,
            validation_data=validation_data,
            epochs=epochs,
            batch_size=batch_size,
            callbacks=callbacks,
            verbose=1
        )
        
        self.is_trained = True
        
        # Get final metrics
        final_loss = self.history.history['loss'][-1]
        final_mae = self.history.history['mae'][-1]
        
        results = {
            'training_complete': True,
            'final_loss': final_loss,
            'final_mae': final_mae,
            'epochs_trained': len(self.history.history['loss'])
        }
        
        if validation_data:
            results['val_loss'] = self.history.history['val_loss'][-1]
            results['val_mae'] = self.history.history['val_mae'][-1]
        
        logger.info(f"Model trained. Final loss: {final_loss:.4f}")
        return results
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        Make predictions using the trained model
        
        Args:
            X (np.ndarray): Sequences for prediction
            
        Returns:
            np.ndarray: Predictions
        """
        if not self.is_trained:
            raise ValueError("Model not trained yet. Call train() first.")
            
        predictions = self.model.predict(X, verbose=0)
        logger.info(f"Made predictions for {len(predictions)} samples")
        return predictions.flatten()
    
    def evaluate(self, X_test: np.ndarray, y_test: np.ndarray) -> Dict:
        """
        Evaluate model performance on test data
        
        Args:
            X_test (np.ndarray): Test sequences
            y_test (np.ndarray): Test targets
            
        Returns:
            Dict: Evaluation metrics
        """
        if not self.is_trained:
            raise ValueError("Model not trained yet. Call train() first.")
            
        # Make predictions
        y_pred = self.predict(X_test)
        
        # Calculate metrics
        r2 = r2_score(y_test, y_pred)
        mse = mean_squared_error(y_test, y_pred)
        mae = mean_absolute_error(y_test, y_pred)
        rmse = np.sqrt(mse)
        
        # Calculate MAPE - improved calculation
        # Only calculate MAPE for non-zero actual values to avoid division issues
        non_zero_mask = y_test > 1e-6  # Use a reasonable threshold
        if non_zero_mask.sum() > 0:
            mape = np.mean(np.abs((y_test[non_zero_mask] - y_pred[non_zero_mask]) / y_test[non_zero_mask])) * 100
        else:
            mape = float('inf')  # If all values are zero, MAPE is undefined
        
        metrics = {
            'r2_score': r2,
            'mse': mse,
            'mae': mae,
            'rmse': rmse,
            'mape': mape
        }
        
        logger.info(f"Evaluation results - R²: {r2:.4f}, RMSE: {rmse:.2f}, MAE: {mae:.2f}")
        return metrics
    
    def save_model(self, filepath: str):
        """
        Save trained model to file
        
        Args:
            filepath (str): Path to save model
        """
        if not self.is_trained:
            raise ValueError("Model not trained yet. Call train() first.")
            
        self.model.save(filepath)
        logger.info(f"Model saved to {filepath}")
    
    def load_model(self, filepath: str):
        """
        Load trained model from file
        
        Args:
            filepath (str): Path to load model
        """
        if not TENSORFLOW_AVAILABLE:
            raise ImportError("TensorFlow is required to load models")
            
        self.model = tf.keras.models.load_model(filepath)
        self.is_trained = True
        logger.info(f"Model loaded from {filepath}")

def prepare_lstm_dataset(dataset: Dict, sequence_length: int = 10) -> Dict:
    """
    Prepare dataset for LSTM/GRU training
    
    Args:
        dataset (Dict): Original dataset from data_preparation
        sequence_length (int): Length of sequences
        
    Returns:
        Dict: LSTM-ready dataset
    """
    logger.info("Preparing dataset for LSTM/GRU training")
    
    # Create predictor
    predictor = LSTMDemandPredictor()
    predictor.sequence_length = sequence_length
    
    # Prepare sequences
    X_train_seq, y_train_seq = predictor.prepare_sequences(
        dataset['X_train'], dataset['y_train'], sequence_length
    )
    
    X_test_seq, y_test_seq = predictor.prepare_sequences(
        dataset['X_test'], dataset['y_test'], sequence_length
    )
    
    # For validation, use part of training data
    val_split = int(0.8 * len(X_train_seq))
    X_val_seq = X_train_seq[val_split:]
    y_val_seq = y_train_seq[val_split:]
    X_train_seq_final = X_train_seq[:val_split]
    y_train_seq_final = y_train_seq[:val_split]
    
    lstm_dataset = {
        'X_train': X_train_seq_final,
        'X_val': X_val_seq,
        'X_test': X_test_seq,
        'y_train': y_train_seq_final,
        'y_val': y_val_seq,
        'y_test': y_test_seq,
        'sequence_length': sequence_length,
        'feature_names': dataset['feature_names']
    }
    
    logger.info("LSTM dataset prepared successfully")
    return lstm_dataset

def train_lstm_model(lstm_dataset: Dict, model_type: str = 'LSTM') -> Tuple[LSTMDemandPredictor, Dict]:
    """
    Train LSTM/GRU model with the prepared dataset
    
    Args:
        lstm_dataset (Dict): LSTM-ready dataset
        model_type (str): 'LSTM' or 'GRU'
        
    Returns:
        Tuple: (trained_model, training_results)
    """
    logger.info(f"Starting {model_type} model training")
    
    # Initialize model
    model = LSTMDemandPredictor(model_type)
    
    # Build model
    input_shape = (lstm_dataset['sequence_length'], len(lstm_dataset['feature_names']))
    model.model = model.build_model(input_shape)
    
    # Train model
    training_results = model.train(
        lstm_dataset['X_train'], lstm_dataset['y_train'],
        lstm_dataset['X_val'], lstm_dataset['y_val'],
        epochs=500,  # Reduced epochs for faster training
        batch_size=32
    )
    
    # Evaluate on test set
    test_metrics = model.evaluate(lstm_dataset['X_test'], lstm_dataset['y_test'])
    training_results['test_metrics'] = test_metrics
    
    logger.info(f"{model_type} model training completed")
    return model, training_results

if __name__ == "__main__":
    # Test LSTM/GRU model
    print("LSTM/GRU Demand Prediction Model")
    print("This module implements deep learning models for time series demand forecasting.")