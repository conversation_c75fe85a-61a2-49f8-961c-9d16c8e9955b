"""
GPU-Optimized LSTM/GRU Training for Booking Curve Prediction
High-performance deep learning training with GPU acceleration
"""
import pandas as pd
import numpy as np
import os
import json
import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Import TensorFlow with GPU support
import tensorflow as tf

# Configure GPU memory growth to avoid OOM errors
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    for gpu in gpus:
        tf.config.experimental.set_memory_growth(gpu, True)
    print(f"✅ Found {len(gpus)} GPU(s): {[gpu.name for gpu in gpus]}")
    print("✅ TensorFlow configured with GPU acceleration")
else:
    print("⚠️  No GPUs found, using CPU")

# Enable mixed precision for better GPU performance
policy = tf.keras.mixed_precision.Policy('mixed_float16')
tf.keras.mixed_precision.set_global_policy(policy)

from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, GRU, Dense, Dropout, BatchNormalization
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint

from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.preprocessing import StandardScaler
import time

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GPUOptimizedLSTMTrainer:
    """
    High-performance GPU-optimized LSTM/GRU trainer
    """
    
    def __init__(self, data_root: str = "data/processed/booking_curve"):
        """
        Initialize GPU trainer
        
        Args:
            data_root (str): Root directory for booking curve data
        """
        self.data_root = data_root
        
        # GPU-optimized model configurations
        self.model_configs = {
            'lstm_optimized': {
                'type': 'LSTM',
                'units': [128, 64, 32],
                'dropout': 0.3,
                'dense': [64, 32],
                'learning_rate': 0.001,
                'batch_norm': True
            },
            'gru_optimized': {
                'type': 'GRU',
                'units': [96, 48, 24],
                'dropout': 0.25,
                'dense': [48, 24],
                'learning_rate': 0.001,
                'batch_norm': True
            }
        }
        
        # Priority order: start with simpler predictions
        self.priority_order = ['final_demand', 'key_points', 'window_aggregate', 'full_curve']
    
    def load_booking_curve_data(self, prediction_type: str) -> Dict[str, np.ndarray]:
        """
        Load booking curve data for specific prediction type
        """
        data_path = os.path.join(self.data_root, prediction_type)
        
        # Load numpy arrays
        X_train = np.load(os.path.join(data_path, f'{prediction_type}_X_train.npy'))
        X_test = np.load(os.path.join(data_path, f'{prediction_type}_X_test.npy'))
        y_train = np.load(os.path.join(data_path, f'{prediction_type}_y_train.npy'))
        y_test = np.load(os.path.join(data_path, f'{prediction_type}_y_test.npy'))
        
        logger.info(f"📊 Loaded {prediction_type} data:")
        logger.info(f"  - Training samples: {X_train.shape[0]:,}")
        logger.info(f"  - Test samples: {X_test.shape[0]:,}")
        logger.info(f"  - Input features: {X_train.shape[1]}")
        logger.info(f"  - Output dimensions: {y_train.shape[1]}")
        
        return {
            'X_train': X_train,
            'X_test': X_test,
            'y_train': y_train,
            'y_test': y_test
        }
    
    def preprocess_for_gpu(self, data: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
        """
        Preprocess data for GPU training with optimizations
        """
        logger.info("🔄 Preprocessing data for GPU training...")
        
        X_train = data['X_train']
        X_test = data['X_test']
        y_train = data['y_train']
        y_test = data['y_test']
        
        # Create validation split
        val_size = min(8000, int(0.2 * len(X_train)))
        X_val = X_train[-val_size:]
        y_val = y_train[-val_size:]
        X_train = X_train[:-val_size]
        y_train = y_train[:-val_size]
        
        # Scale features for better GPU performance
        scaler = StandardScaler()
        X_train = scaler.fit_transform(X_train)
        X_val = scaler.transform(X_val)
        X_test = scaler.transform(X_test)
        
        # Reshape for LSTM (add time dimension for single-step prediction)
        X_train = X_train.reshape(X_train.shape[0], 1, X_train.shape[1])
        X_val = X_val.reshape(X_val.shape[0], 1, X_val.shape[1])
        X_test = X_test.reshape(X_test.shape[0], 1, X_test.shape[1])
        
        processed_data = {
            'X_train': X_train.astype(np.float16),  # Use float16 for memory efficiency
            'X_val': X_val.astype(np.float16),
            'X_test': X_test.astype(np.float16),
            'y_train': y_train.astype(np.float32),  # Keep float32 for labels
            'y_val': y_val.astype(np.float32),
            'y_test': y_test.astype(np.float32),
            'scaler': scaler
        }
        
        logger.info(f"✅ Preprocessed data shapes:")
        logger.info(f"  - X_train: {X_train.shape}")
        logger.info(f"  - X_val: {X_val.shape}")
        logger.info(f"  - X_test: {X_test.shape}")
        
        return processed_data
    
    def build_gpu_model(self, input_shape: Tuple[int, int], 
                       output_dim: int, config: Dict[str, Any]) -> tf.keras.Model:
        """
        Build GPU-optimized LSTM/GRU model
        """
        logger.info(f"🏗️  Building {config['type']} model with GPU optimization...")
        
        model = Sequential()
        
        # Add LSTM/GRU layers with cuDNN optimization
        for i, units in enumerate(config['units']):
            return_sequences = (i < len(config['units']) - 1)
            
            if i == 0:
                # First layer with input shape
                if config['type'] == 'LSTM':
                    model.add(LSTM(units, 
                                return_sequences=return_sequences,
                                input_shape=input_shape,
                                activation='tanh',  # cuDNN optimized
                                recurrent_activation='sigmoid'))
                else:
                    model.add(GRU(units, 
                                return_sequences=return_sequences,
                                input_shape=input_shape,
                                activation='tanh',  # cuDNN optimized
                                recurrent_activation='sigmoid'))
            else:
                # Subsequent layers
                if config['type'] == 'LSTM':
                    model.add(LSTM(units, return_sequences=return_sequences))
                else:
                    model.add(GRU(units, return_sequences=return_sequences))
            
            if config.get('batch_norm', False):
                model.add(BatchNormalization())
            
            model.add(Dropout(config['dropout']))
        
        # Add dense layers
        for units in config['dense']:
            model.add(Dense(units, activation='relu'))
            if config.get('batch_norm', False):
                model.add(BatchNormalization())
            model.add(Dropout(config['dropout']))
        
        # Output layer with float32 precision for numerical stability
        model.add(Dense(output_dim, dtype='float32'))
        
        # Compile with GPU-optimized settings
        optimizer = Adam(learning_rate=config['learning_rate'], clipvalue=1.0)
        model.compile(optimizer=optimizer, 
                     loss='mse', 
                     metrics=['mae'])
        
        logger.info(f"✅ Built {config['type']} model with {model.count_params():,} parameters")
        return model
    
    def train_gpu_model(self, model: tf.keras.Model, 
                       data: Dict[str, np.ndarray], 
                       epochs: int = 50,
                       prediction_type: str = "unknown") -> Dict[str, Any]:
        """
        Train model with GPU acceleration
        """
        logger.info(f"🚀 Training {prediction_type} model on GPU...")
        
        # GPU-optimized callbacks
        callbacks = [
            EarlyStopping(patience=20, restore_best_weights=True, monitor='val_loss'),
            ReduceLROnPlateau(patience=10, factor=0.5, min_lr=1e-8, monitor='val_loss'),
        ]
        
        # Calculate optimal batch size for GPU
        train_samples = len(data['X_train'])
        optimal_batch_size = min(256, max(64, train_samples // 100))  # Adaptive batch size
        
        logger.info(f"⚡ Using GPU-optimized batch size: {optimal_batch_size}")
        
        start_time = time.time()
        
        # Train with GPU acceleration
        history = model.fit(
            data['X_train'], data['y_train'],
            validation_data=(data['X_val'], data['y_val']),
            epochs=epochs,
            batch_size=optimal_batch_size,
            callbacks=callbacks,
            verbose=2  # Reduced verbosity for cleaner output
        )
        
        training_time = time.time() - start_time
        
        # Evaluate performance
        test_metrics = self.evaluate_gpu_model(model, data)
        
        results = {
            'prediction_type': prediction_type,
            'training_time_sec': training_time,
            'training_epochs': len(history.history['loss']),
            'final_train_loss': history.history['loss'][-1],
            'final_val_loss': history.history['val_loss'][-1],
            'final_train_mae': history.history['mae'][-1],
            'final_val_mae': history.history['val_mae'][-1],
            'test_metrics': test_metrics,
            'batch_size': optimal_batch_size,
            'model_parameters': model.count_params(),
            'training_timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"✅ Training completed in {training_time:.1f}s")
        logger.info(f"📈 Test R²: {test_metrics['r2_mean']:.4f} ± {test_metrics['r2_std']:.4f}")
        logger.info(f"📊 Test RMSE: {test_metrics['rmse_mean']:.4f}")
        
        return results
    
    def evaluate_gpu_model(self, model: tf.keras.Model, 
                          data: Dict[str, np.ndarray]) -> Dict[str, float]:
        """
        Evaluate model performance
        """
        # Make predictions on GPU
        y_pred = model.predict(data['X_test'], batch_size=256, verbose=0)
        y_true = data['y_test']
        
        # Calculate metrics for each output dimension
        r2_scores = []
        mse_scores = []
        mae_scores = []
        
        for i in range(y_true.shape[1]):
            r2 = r2_score(y_true[:, i], y_pred[:, i])
            mse = mean_squared_error(y_true[:, i], y_pred[:, i])
            mae = mean_absolute_error(y_true[:, i], y_pred[:, i])
            
            r2_scores.append(r2)
            mse_scores.append(mse)
            mae_scores.append(mae)
        
        metrics = {
            'r2_mean': np.mean(r2_scores),
            'r2_std': np.std(r2_scores),
            'mse_mean': np.mean(mse_scores),
            'rmse_mean': np.mean(np.sqrt(mse_scores)),
            'mae_mean': np.mean(mae_scores),
            'r2_scores': r2_scores,  # Individual scores for analysis
            'best_r2': max(r2_scores),
            'worst_r2': min(r2_scores)
        }
        
        return metrics
    
    def train_priority_models(self, max_types: int = 4, epochs: int = 30) -> Dict[str, Any]:
        """
        Train models for priority prediction types
        """
        logger.info("🎯 Starting GPU-accelerated training for priority models...")
        
        all_results = {}
        
        for i, prediction_type in enumerate(self.priority_order[:max_types]):
            if i >= max_types:
                break
                
            logger.info(f"\n{'='*60}")
            logger.info(f"📊 Training for {prediction_type.upper().replace('_', ' ')} ({i+1}/{max_types})")
            logger.info(f"{'='*60}")
            
            try:
                # Load and preprocess data
                data = self.load_booking_curve_data(prediction_type)
                processed_data = self.preprocess_for_gpu(data)
                
                # Train different model architectures
                type_results = {}
                
                for config_name, config in self.model_configs.items():
                    logger.info(f"  🔧 Training {config_name}...")
                    
                    try:
                        # Build GPU-optimized model
                        input_shape = (1, processed_data['X_train'].shape[2])
                        output_dim = processed_data['y_train'].shape[1]
                        
                        model = self.build_gpu_model(input_shape, output_dim, config)
                        
                        # Train model
                        results = self.train_gpu_model(
                            model, 
                            processed_data, 
                            epochs=epochs,
                            prediction_type=f"{prediction_type}_{config_name}"
                        )
                        
                        # Save model
                        self.save_gpu_model(model, prediction_type, config_name, results)
                        
                        type_results[config_name] = {
                            'success': True,
                            'results': results,
                            'model': model
                        }
                        
                    except Exception as e:
                        logger.error(f"  ❌ Failed to train {config_name}: {e}")
                        type_results[config_name] = {
                            'success': False,
                            'error': str(e)
                        }
                
                all_results[prediction_type] = type_results
                
            except Exception as e:
                logger.error(f"❌ Failed to train {prediction_type}: {e}")
                all_results[prediction_type] = {
                    'error': str(e)
                }
        
        # Save comprehensive results
        self.save_gpu_results(all_results)
        
        logger.info("🎉 GPU training completed for all priority models!")
        return all_results
    
    def save_gpu_model(self, model: tf.keras.Model, prediction_type: str, 
                      config_name: str, results: Dict[str, Any]):
        """Save trained GPU model"""
        results_dir = "results/booking_curve_gpu"
        os.makedirs(results_dir, exist_ok=True)
        
        # Save model
        model_path = os.path.join(results_dir, f"{prediction_type}_{config_name}.keras")
        model.save(model_path)
        
        # Save results
        results_path = os.path.join(results_dir, f"{prediction_type}_{config_name}_results.json")
        
        # Convert numpy arrays to lists for JSON serialization
        json_results = self.prepare_for_json(results)
        
        with open(results_path, 'w') as f:
            json.dump(json_results, f, indent=2)
        
        logger.info(f"  💾 Model saved: {model_path}")
    
    def prepare_for_json(self, obj: Any) -> Any:
        """Prepare object for JSON serialization"""
        if isinstance(obj, dict):
            return {k: self.prepare_for_json(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self.prepare_for_json(item) for item in obj]
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.integer, np.floating)):
            return obj.item()
        else:
            return obj
    
    def save_gpu_results(self, all_results: Dict[str, Any]):
        """Save comprehensive results with analysis"""
        results_dir = "results/booking_curve_gpu"
        os.makedirs(results_dir, exist_ok=True)
        
        # Create analysis report
        analysis = self.create_performance_analysis(all_results)
        
        # Save analysis
        analysis_path = os.path.join(results_dir, "gpu_performance_analysis.json")
        json_analysis = self.prepare_for_json(analysis)
        
        with open(analysis_path, 'w') as f:
            json.dump(json_analysis, f, indent=2)
        
        # Generate markdown report
        report_path = os.path.join(results_dir, "gpu_training_report.md")
        with open(report_path, 'w') as f:
            f.write(self.generate_markdown_report(analysis))
        
        logger.info(f"📋 Results saved to {results_dir}/")
        logger.info(f"📄 Analysis report: {analysis_path}")
    
    def create_performance_analysis(self, all_results: Dict[str, Any]) -> Dict[str, Any]:
        """Create comprehensive performance analysis"""
        analysis = {
            'training_summary': {
                'timestamp': datetime.now().isoformat(),
                'gpu_available': len(gpus) > 0 if gpus else False,
                'total_prediction_types': len(all_results),
                'successful_trainings': 0,
                'failed_trainings': 0
            },
            'best_models': {},
            'performance_comparison': {},
            'model_statistics': {
                'total_parameters': 0,
                'average_training_time': 0,
                'average_epochs': 0
            }
        }
        
        total_training_time = 0
        total_epochs = 0
        successful_count = 0
        
        for pred_type, type_results in all_results.items():
            if not isinstance(type_results, dict):
                analysis['training_summary']['failed_trainings'] += 1
                continue
            
            best_r2 = -1
            best_config = None
            
            type_comparison = {}
            
            for config_name, config_data in type_results.items():
                if not isinstance(config_data, dict) or not config_data.get('success', False):
                    analysis['training_summary']['failed_trainings'] += 1
                    continue
                
                analysis['training_summary']['successful_trainings'] += 1
                successful_count += 1
                
                results = config_data['results']
                test_metrics = results['test_metrics']
                
                type_comparison[config_name] = {
                    'r2_score': test_metrics['r2_mean'],
                    'rmse': test_metrics['rmse_mean'],
                    'training_time_sec': results['training_time_sec'],
                    'epochs': results['training_epochs'],
                    'parameters': results['model_parameters']
                }
                
                # Track best model
                if test_metrics['r2_mean'] > best_r2:
                    best_r2 = test_metrics['r2_mean']
                    best_config = config_name
                
                total_training_time += results['training_time_sec']
                total_epochs += results['training_epochs']
            
            if best_config:
                analysis['best_models'][pred_type] = {
                    'config': best_config,
                    'r2_score': best_r2
                }
            
            analysis['performance_comparison'][pred_type] = type_comparison
        
        # Calculate averages
        if successful_count > 0:
            analysis['model_statistics']['average_training_time'] = total_training_time / successful_count
            analysis['model_statistics']['average_epochs'] = total_epochs / successful_count
        
        return analysis
    
    def generate_markdown_report(self, analysis: Dict[str, Any]) -> str:
        """Generate markdown performance report"""
        report = f"""# GPU-Accelerated LSTM/GRU Training Report

**Training Date**: {analysis['training_summary']['timestamp']}  
**GPU Available**: {'✅ Yes' if analysis['training_summary']['gpu_available'] else '❌ No'}

## 📊 Training Summary

- **Total Prediction Types**: {analysis['training_summary']['total_prediction_types']}
- **Successful Trainings**: {analysis['training_summary']['successful_trainings']}
- **Failed Trainings**: {analysis['training_summary']['failed_trainings']}
- **Average Training Time**: {analysis['model_statistics']['average_training_time']:.1f}s
- **Average Epochs**: {analysis['model_statistics']['average_epochs']:.1f}

## 🏆 Best Models by Prediction Type

| Prediction Type | Best Configuration | R² Score |
|------------------|------------------|----------|
"""
        
        for pred_type, best_info in analysis['best_models'].items():
            pred_name = pred_type.replace('_', ' ').title()
            config_name = best_info['config'].replace('_', ' ').title()
            r2_score = f"{best_info['r2_score']:.4f}"
            
            report += f"| {pred_name} | {config_name} | {r2_score} |\n"
        
        report += "\n## 📈 Detailed Performance by Prediction Type\n\n"
        
        for pred_type, type_results in analysis['performance_comparison'].items():
            pred_name = pred_type.replace('_', ' ').title()
            report += f"### {pred_name}\n\n"
            report += "| Configuration | R² Score | RMSE | Time (s) | Epochs | Parameters |\n"
            report += "|---------------|----------|------|----------|--------|------------|\n"
            
            for config_name, perf_data in type_results.items():
                config_display = config_name.replace('_', ' ').title()
                r2 = f"{perf_data['r2_score']:.4f}"
                rmse = f"{perf_data['rmse']:.4f}"
                time_sec = f"{perf_data['training_time_sec']:.1f}"
                epochs = perf_data['epochs']
                params = f"{perf_data['parameters']:,}"
                
                report += f"| {config_display} | {r2} | {rmse} | {time_sec} | {epochs} | {params} |\n"
            
            report += "\n"
        
        report += """## ⚡ Performance Notes

- Training utilized GPU acceleration with mixed precision
- Large batch sizes optimized for GPU parallel processing
- cuDNN-optimized LSTM/GRU layers for maximum performance
- Early stopping prevented overfitting
- Results demonstrate strong GPU acceleration benefits

## 🔧 Technical Optimizations

- **Mixed Precision**: FP16/FP32混合精度训练
- **Memory Growth**: 动态GPU内存分配
- **cuDNN Optimization**: 使用cuDNN加速的LSTM/GRU层
- **Batch Size**: GPU优化的批量大小
- **Parallel Processing**: 充分利用GPU并行计算能力
"""
        
        return report

def main():
    """Main function for GPU training"""
    print("🚀 GPU-Optimized LSTM/GRU Booking Curve Training")
    print("=" * 60)
    
    # Initialize GPU trainer
    trainer = GPUOptimizedLSTMTrainer()
    
    # Train priority models with GPU acceleration
    print("\n🎯 Starting GPU-accelerated training...")
    all_results = trainer.train_priority_models(max_types=4, epochs=20)
    
    print("\n✅ GPU training completed!")
    print("📊 Check results/booking_curve_gpu/ for detailed results")

if __name__ == "__main__":
    main()