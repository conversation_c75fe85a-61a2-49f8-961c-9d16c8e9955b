"""
Total Demand Prediction using XGBoost
Main training script for airline flight total demand prediction
"""
import pandas as pd
import numpy as np
import os
import json
import joblib
import logging
from datetime import datetime
from typing import Dict, Tuple, Any

# Import our XGBoost model
from xgboost_model import XGBoostDemandPredictor, train_xgboost_model

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('total_demand_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TotalDemandTrainer:
    """
    Total Demand Prediction Trainer for airline flights
    """
    
    def __init__(self, data_path: str = None):
        """
        Initialize trainer
        
        Args:
            data_path (str): Path to processed data directory
        """
        if data_path is None:
            data_path = "/home/<USER>/cem208/code/ie2025/data/processed"
        
        self.data_path = data_path
        self.model = None
        self.training_results = {}
        
        logger.info(f"TotalDemandTrainer initialized with data path: {data_path}")
    
    def load_total_demand_data(self) -> Dict[str, Any]:
        """
        Load total demand prediction data
        
        Returns:
            Dict: Dictionary containing train/test data and metadata
        """
        logger.info("Loading total demand prediction data...")
        
        # Define file paths
        files = {
            'X_train': os.path.join(self.data_path, 'total_demand_X_train.csv'),
            'X_test': os.path.join(self.data_path, 'total_demand_X_test.csv'),
            'y_train': os.path.join(self.data_path, 'total_demand_y_train.csv'),
            'y_test': os.path.join(self.data_path, 'total_demand_y_test.csv'),
            'features': os.path.join(self.data_path, 'total_demand_features.csv'),
            'scaler': os.path.join(self.data_path, 'total_demand_scaler.pkl'),
            'info': os.path.join(self.data_path, 'total_demand_dataset_info.json')
        }
        
        # Validate file existence
        for file_name, file_path in files.items():
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"Missing file: {file_path}")
        
        # Load data
        try:
            X_train = pd.read_csv(files['X_train'])
            X_test = pd.read_csv(files['X_test'])
            y_train = pd.read_csv(files['y_train']).iloc[:, 0]  # First column
            y_test = pd.read_csv(files['y_test']).iloc[:, 0]    # First column
            features = pd.read_csv(files['features'])
            scaler = joblib.load(files['scaler'])
            
            with open(files['info'], 'r') as f:
                info = json.load(f)
            
            dataset = {
                'X_train': X_train,
                'X_test': X_test,
                'y_train': y_train,
                'y_test': y_test,
                'features': features,
                'scaler': scaler,
                'info': info,
                'file_paths': files
            }
            
            logger.info(f"Data loaded successfully:")
            logger.info(f"  - Training samples: {X_train.shape[0]}")
            logger.info(f"  - Test samples: {X_test.shape[0]}")
            logger.info(f"  - Features: {X_train.shape[1]}")
            logger.info(f"  - Target range: {y_train.min():.2f} - {y_train.max():.2f}")
            
            return dataset
            
        except Exception as e:
            logger.error(f"Error loading data: {str(e)}")
            raise
    
    def analyze_data_quality(self, dataset: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze data quality and provide insights
        
        Args:
            dataset (Dict): Dataset dictionary
            
        Returns:
            Dict: Data quality analysis results
        """
        logger.info("Analyzing data quality...")
        
        X_train, y_train = dataset['X_train'], dataset['y_train']
        X_test, y_test = dataset['X_test'], dataset['y_test']
        
        analysis = {
            'data_shape': {
                'train_features': X_train.shape,
                'test_features': X_test.shape,
                'train_target': y_train.shape,
                'test_target': y_test.shape
            },
            'target_statistics': {
                'train_mean': float(y_train.mean()),
                'train_std': float(y_train.std()),
                'train_min': float(y_train.min()),
                'train_max': float(y_train.max()),
                'test_mean': float(y_test.mean()),
                'test_std': float(y_test.std()),
                'test_min': float(y_test.min()),
                'test_max': float(y_test.max())
            },
            'feature_analysis': {
                'total_features': X_train.shape[1],
                'missing_values_train': int(X_train.isnull().sum().sum()),
                'missing_values_test': int(X_test.isnull().sum().sum()),
                'feature_types': X_train.dtypes.value_counts().to_dict()
            },
            'data_distribution': {
                'train_test_ratio': f"{X_train.shape[0]/(X_train.shape[0]+X_test.shape[0]):.2f}",
                'target_correlation_with_features': {}
            }
        }
        
        # Calculate feature correlations with target (sample for performance)
        sample_size = min(1000, len(X_train))
        if sample_size > 0:
            sample_X = X_train.iloc[:sample_size]
            sample_y = y_train.iloc[:sample_size]
            
            # Calculate correlations numeric features only
            numeric_features = sample_X.select_dtypes(include=[np.number]).columns
            if len(numeric_features) > 0:
                correlations = sample_X[numeric_features].corrwith(sample_y).abs().sort_values(ascending=False)
                analysis['data_distribution']['top_correlated_features'] = {
                    str(feature): float(correlations[feature]) 
                    for feature in correlations.head(10).index
                }
        
        logger.info("Data quality analysis completed")
        return analysis
    
    def train_total_demand_model(self, 
                                dataset: Dict[str, Any],
                                perform_hyperparameter_tuning: bool = True,
                                save_model: bool = True) -> Tuple[XGBoostDemandPredictor, Dict[str, Any]]:
        """
        Train total demand prediction model
        
        Args:
            dataset (Dict): Dataset dictionary
            perform_hyperparameter_tuning (bool): Whether to perform hyperparameter tuning
            save_model (bool): Whether to save the trained model
            
        Returns:
            Tuple: (trained_model, training_results)
        """
        logger.info("Starting total demand model training...")
        
        # Initialize XGBoost predictor
        self.model = XGBoostDemandPredictor()
        
        # Perform hyperparameter tuning if requested
        if perform_hyperparameter_tuning:
            logger.info("Performing hyperparameter tuning...")
            tuning_results = self.model.hyperparameter_tuning(
                dataset['X_train'], 
                dataset['y_train']
            )
            self.training_results['tuning'] = tuning_results
            logger.info("Hyperparameter tuning completed")
        else:
            self.model.initialize_model()
        
        # Train model
        logger.info("Training model...")
        training_results = self.model.train(dataset['X_train'], dataset['y_train'])
        self.training_results.update(training_results)
        
        # Evaluate model
        logger.info("Evaluating model...")
        test_metrics = self.model.evaluate(dataset['X_test'], dataset['y_test'])
        self.training_results['test_metrics'] = test_metrics
        
        # Log results
        logger.info("Training Results:")
        logger.info(f"  - CV R²: {self.training_results.get('cv_mean_r2', 0):.4f}")
        logger.info(f"  - Test R²: {test_metrics['r2_score']:.4f}")
        logger.info(f"  - Test RMSE: {test_metrics['rmse']:.2f}")
        logger.info(f"  - Test MAE: {test_metrics['mae']:.2f}")
        logger.info(f"  - Test MAPE: {test_metrics['mape']:.2f}%")
        
        # Save model if requested
        if save_model:
            self.save_model_and_results()
        
        logger.info("Model training completed successfully")
        return self.model, self.training_results
    
    def save_model_and_results(self):
        """
        Save trained model and training results
        """
        logger.info("Saving model and results...")
        
        # Create results directory
        results_dir = "/home/<USER>/cem208/code/ie2025/results/total_demand"
        os.makedirs(results_dir, exist_ok=True)
        
        # Generate timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save model
        model_path = os.path.join(results_dir, f"xgboost_total_demand_model_{timestamp}.joblib")
        self.model.save_model(model_path)
        
        # Save results
        results_path = os.path.join(results_dir, f"training_results_{timestamp}.json")
        results_to_save = {
            'timestamp': timestamp,
            'model_type': 'XGBoost',
            'prediction_target': 'total_demand',
            'training_results': self._convert_for_json(self.training_results),
            'model_path': model_path
        }
        
        with open(results_path, 'w') as f:
            json.dump(results_to_save, f, indent=2)
        
        # Save feature importance
        if self.model.feature_importance is not None:
            feature_importance_path = os.path.join(results_dir, f"feature_importance_{timestamp}.csv")
            self.model.feature_importance.to_csv(feature_importance_path, index=False)
        
        logger.info(f"Model and results saved to {results_dir}")
        logger.info(f"  - Model: {model_path}")
        logger.info(f"  - Results: {results_path}")
        logger.info(f"  - Feature importance: {feature_importance_path}")
    
    def _convert_for_json(self, obj):
        """
        Convert numpy and pandas objects to JSON-serializable format
        """
        if isinstance(obj, dict):
            return {key: self._convert_for_json(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_for_json(item) for item in obj]
        elif isinstance(obj, pd.DataFrame):
            return obj.to_dict()
        elif isinstance(obj, pd.Series):
            return obj.to_dict()
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.bool_):
            return bool(obj)
        else:
            return obj
    
    def generate_predictions_report(self, dataset: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate detailed predictions report
        
        Args:
            dataset (Dict): Dataset dictionary
            
        Returns:
            Dict: Predictions report
        """
        logger.info("Generating predictions report...")
        
        if self.model is None or not self.model.is_trained:
            raise ValueError("Model not trained yet")
        
        # Make predictions on test set
        X_test, y_test = dataset['X_test'], dataset['y_test']
        y_pred = self.model.predict(X_test)
        
        # Create prediction DataFrame
        predictions_df = pd.DataFrame({
            'actual': y_test.values,
            'predicted': y_pred,
            'residual': y_test.values - y_pred,
            'absolute_error': np.abs(y_test.values - y_pred),
            'percentage_error': np.abs((y_test.values - y_pred) / (y_test.values + 1e-6)) * 100
        })
        
        # Analyze predictions
        report = {
            'prediction_statistics': {
                'total_predictions': len(y_pred),
                'mean_predicted': float(y_pred.mean()),
                'std_predicted': float(y_pred.std()),
                'min_predicted': float(y_pred.min()),
                'max_predicted': float(y_pred.max())
            },
            'error_analysis': {
                'mean_absolute_error': float(predictions_df['absolute_error'].mean()),
                'mean_percentage_error': float(predictions_df['percentage_error'].mean()),
                'max_absolute_error': float(predictions_df['absolute_error'].max()),
                'predictions_within_10_percent': int((predictions_df['percentage_error'] <= 10).sum()),
                'predictions_within_20_percent': int((predictions_df['percentage_error'] <= 20).sum())
            },
            'sample_predictions': predictions_df.head(20).to_dict('records')
        }
        
        logger.info("Predictions report generated")
        return report


def main():
    """
    Main execution function
    """
    logger.info("=" * 60)
    logger.info("TOTAL DEMAND PREDICTION - XGBOOST MODEL TRAINING")
    logger.info("=" * 60)
    
    try:
        # Initialize trainer
        trainer = TotalDemandTrainer()
        
        # Load data
        logger.info("Step 1: Loading data...")
        dataset = trainer.load_total_demand_data()
        
        # Analyze data quality
        logger.info("Step 2: Analyzing data quality...")
        quality_analysis = trainer.analyze_data_quality(dataset)
        logger.info(f"Data quality: {quality_analysis['feature_analysis']['missing_values_train']} missing values in training")
        
        # Train model
        logger.info("Step 3: Training model...")
        model, training_results = trainer.train_total_demand_model(
            dataset=dataset,
            perform_hyperparameter_tuning=True,
            save_model=True
        )
        
        # Generate predictions report
        logger.info("Step 4: Generating predictions report...")
        predictions_report = trainer.generate_predictions_report(dataset)
        
        # Final summary
        logger.info("=" * 60)
        logger.info("TRAINING COMPLETED SUCCESSFULLY")
        logger.info("=" * 60)
        logger.info("Key Results:")
        logger.info(f"  - Test R²: {training_results['test_metrics']['r2_score']:.4f}")
        logger.info(f"  - Test RMSE: {training_results['test_metrics']['rmse']:.2f}")
        logger.info(f"  - Test MAE: {training_results['test_metrics']['mae']:.2f}")
        logger.info(f"  - Test MAPE: {training_results['test_metrics']['mape']:.2f}%")
        logger.info(f"  - Predictions within 10%: {predictions_report['error_analysis']['predictions_within_10_percent']}")
        logger.info(f"  - Predictions within 20%: {predictions_report['error_analysis']['predictions_within_20_percent']}")
        
        return True
        
    except Exception as e:
        logger.error(f"Training failed: {str(e)}")
        logger.error("Please check the logs for detailed error information")
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ Total demand prediction model training completed successfully!")
    else:
        print("\n❌ Training failed. Please check the logs.")
        exit(1)