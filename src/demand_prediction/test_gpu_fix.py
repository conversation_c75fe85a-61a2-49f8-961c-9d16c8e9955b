"""
Test script for GPU cuDNN compatibility fix
"""
import tensorflow as tf
import numpy as np
import warnings
warnings.filterwarnings('ignore')

def test_gpu_compatibility():
    """Test GPU and cuDNN compatibility with fallback"""
    print("🔧 GPU和cuDNN兼容性测试")
    print("=" * 50)
    
    # Configure GPU memory growth
    gpus = tf.config.experimental.list_physical_devices('GPU')
    if gpus:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print(f"✅ 检测到 {len(gpus)} 个GPU")
    else:
        print("⚠️  未检测到GPU，使用CPU")
        return False
    
    # Test cuDNN compatibility
    print("\n🧪 测试cuDNN兼容性...")
    try:
        # Simple cuDNN test
        test_input = tf.random.normal((32, 10, 20))
        test_lstm = tf.keras.layers.LSTM(32, activation='tanh', recurrent_activation='sigmoid')
        output = test_lstm(test_input)
        print("✅ cuDNN LSTM可用")
        use_cudnn = True
    except Exception as e:
        print(f"❌ cuDNN LSTM不可用: {str(e)[:100]}...")
        print("🔄 将使用非cuDNN实现")
        use_cudnn = False
    
    # Test fallback implementation
    print("\n🔄 测试fallback实现...")
    try:
        test_input = tf.random.normal((32, 10, 20))
        if use_cudnn:
            # cuDNN version
            lstm_layer = tf.keras.layers.LSTM(32, activation='tanh', recurrent_activation='sigmoid')
        else:
            # Non-cuDNN version
            lstm_layer = tf.keras.layers.LSTM(32, implementation=1)
        
        output = lstm_layer(test_input)
        print(f"✅ LSTM fallback测试成功，输出形状: {output.shape}")
        
    except Exception as e:
        print(f"❌ LSTM fallback测试失败: {e}")
        return False
    
    # Test complete model
    print("\n🏗️  测试完整模型...")
    try:
        model = tf.keras.Sequential([
            tf.keras.layers.LSTM(64, return_sequences=True, input_shape=(10, 20),
                               implementation=1 if not use_cudnn else 2),
            tf.keras.layers.LSTM(32, implementation=1 if not use_cudnn else 2),
            tf.keras.layers.Dense(16, activation='relu'),
            tf.keras.layers.Dense(1)
        ])
        
        model.compile(optimizer='adam', loss='mse', metrics=['mae'])
        
        # Test training
        X = np.random.random((100, 10, 20)).astype(np.float32)
        y = np.random.random((100, 1)).astype(np.float32)
        
        history = model.fit(X, y, epochs=2, batch_size=32, verbose=0)
        print(f"✅ 模型训练测试成功，最终损失: {history.history['loss'][-1]:.4f}")
        
    except Exception as e:
        print(f"❌ 完整模型测试失败: {e}")
        return False
    
    print(f"\n🎉 GPU兼容性测试完成！")
    print(f"   cuDNN状态: {'可用' if use_cudnn else '不可用，使用fallback'}")
    return True

def create_compatible_model(input_shape, output_dim, use_cudnn=None):
    """Create a model compatible with current GPU/cuDNN setup"""
    
    if use_cudnn is None:
        # Auto-detect cuDNN availability
        try:
            test_input = tf.random.normal((1, 10, 20))
            test_lstm = tf.keras.layers.LSTM(32)
            _ = test_lstm(test_input)
            use_cudnn = True
        except:
            use_cudnn = False
    
    print(f"🏗️  创建模型 (cuDNN: {'启用' if use_cudnn else '禁用'})")
    
    model = tf.keras.Sequential()
    
    # LSTM layers with compatibility handling
    if use_cudnn:
        # cuDNN optimized
        model.add(tf.keras.layers.LSTM(128, return_sequences=True, input_shape=input_shape,
                                     activation='tanh', recurrent_activation='sigmoid'))
        model.add(tf.keras.layers.LSTM(64, activation='tanh', recurrent_activation='sigmoid'))
    else:
        # Non-cuDNN fallback
        model.add(tf.keras.layers.LSTM(128, return_sequences=True, input_shape=input_shape,
                                     implementation=1))
        model.add(tf.keras.layers.LSTM(64, implementation=1))
    
    # Dense layers
    model.add(tf.keras.layers.Dense(32, activation='relu'))
    model.add(tf.keras.layers.Dropout(0.2))
    model.add(tf.keras.layers.Dense(output_dim))
    
    # Compile
    model.compile(optimizer='adam', loss='mse', metrics=['mae'])
    
    return model, use_cudnn

def test_demand_prediction_model():
    """Test a realistic demand prediction model"""
    print("\n📊 需求预测模型测试")
    print("=" * 50)
    
    # Simulate realistic data
    batch_size = 1000
    timesteps = 1  # Single timestep for product features
    features = 31  # Number of features (like our no-leakage features)
    
    # Create synthetic data
    X = np.random.random((batch_size, timesteps, features)).astype(np.float32)
    y = np.random.random((batch_size, 1)).astype(np.float32) * 10  # Demand values 0-10
    
    print(f"📈 数据形状: X={X.shape}, y={y.shape}")
    
    # Create compatible model
    model, use_cudnn = create_compatible_model((timesteps, features), 1)
    
    print(f"🎯 模型参数: {model.count_params():,}")
    
    # Train model
    print("🚀 开始训练...")
    history = model.fit(X, y, 
                       epochs=5, 
                       batch_size=64, 
                       validation_split=0.2,
                       verbose=1)
    
    # Evaluate
    final_loss = history.history['loss'][-1]
    final_val_loss = history.history['val_loss'][-1]
    
    print(f"\n📊 训练结果:")
    print(f"   最终训练损失: {final_loss:.4f}")
    print(f"   最终验证损失: {final_val_loss:.4f}")
    print(f"   cuDNN状态: {'启用' if use_cudnn else '禁用'}")
    
    return model, history

if __name__ == "__main__":
    print("🧪 GPU cuDNN兼容性修复测试")
    print("=" * 60)
    
    # Test basic compatibility
    if test_gpu_compatibility():
        # Test realistic model
        model, history = test_demand_prediction_model()
        print("\n✅ 所有测试通过！GPU训练已修复。")
    else:
        print("\n❌ GPU兼容性测试失败")
