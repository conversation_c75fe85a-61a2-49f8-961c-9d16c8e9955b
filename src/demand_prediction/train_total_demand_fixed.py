"""
Train Total Demand Model with Fixed Label Leakage
Removes problematic features and retrains model properly
"""
import pandas as pd
import numpy as np
import joblib
import os
from sklearn.model_selection import GridSearchCV, cross_val_score
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from xgboost import XGBRegressor
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def train_fixed_total_demand_model():
    """
    训练修复标签泄漏后的总需求预测模型
    """
    print("🛡️ 训练修复标签泄漏后的总需求模型...")
    
    # 加载原始数据
    data_path = "/home/<USER>/cem208/code/ie2025/data/processed"
    X_train = pd.read_csv(os.path.join(data_path, "total_demand_X_train.csv"))
    X_test = pd.read_csv(os.path.join(data_path, "total_demand_X_test.csv"))
    y_train = pd.read_csv(os.path.join(data_path, "total_demand_y_train.csv")).iloc[:, 0]
    y_test = pd.read_csv(os.path.join(data_path, "total_demand_y_test.csv")).iloc[:, 0]
    
    print(f"📊 原始数据规模:")
    print(f"  - 训练集: {X_train.shape}")
    print(f"  - 测试集: {X_test.shape}")
    
    # === 识别和移除泄漏特征 ===
    print("🔍 识别泄漏特征...")
    
    # 泄漏特征列表
    leakage_features = [
        'rd_bookings_std',  # 最严重的泄漏特征
        'total_rd_bookings',  # 如果存在
        'has_historical_data'  # 如果存在
    ]
    
    # 所有舱位总量特征 (可能泄漏)
    cabin_total_features = [col for col in X_train.columns if col.endswith('_total')]
    
    # 所有RD相关特征
    rd_related_features = [col for col in X_train.columns if 'rd' in col.lower()]
    
    # 合并所有要移除的特征
    features_to_remove = set(leakage_features + cabin_total_features + rd_related_features)
    features_to_remove = [f for f in features_to_remove if f in X_train.columns]
    
    print(f"🚨 移除的泄漏特征 ({len(features_to_remove)} 个):")
    for feature in features_to_remove:
        print(f"  - {feature}")
    
    # 创建安全的特征集
    safe_features = [col for col in X_train.columns if col not in features_to_remove]
    X_train_safe = X_train[safe_features]
    X_test_safe = X_test[safe_features]
    
    print(f"✅ 安全特征集:")
    print(f"  - 特征数量: {len(safe_features)}")
    print(f"  - 训练集: {X_train_safe.shape}")
    print(f"  - 测试集: {X_test_safe.shape}")
    
    # === 验证无泄漏风险 ===
    print("🛡️ 验证无泄漏风险...")
    
    correlations = {}
    for feature in safe_features[:20]:  # 检查前20个特征
        if X_train_safe[feature].dtype in ['int64', 'float64']:
            corr = X_train_safe[feature].corr(y_train)
            correlations[feature] = corr
    
    high_corr_features = [f for f, c in correlations.items() if abs(corr) > 0.7]
    if high_corr_features:
        print(f"⚠️  发现高相关性特征: {high_corr_features}")
    else:
        print("✅ 未发现异常高相关性，无泄漏风险低")
    
    # === 训练XGBoost模型 ===
    print("🚀 训练XGBoost模型...")
    
    # 使用CPU优化的参数
    xgb_params = {
        'n_estimators': 200,
        'max_depth': 8,
        'learning_rate': 0.1,
        'subsample': 0.8,
        'random_state': 42,
        'n_jobs': -1,
        'tree_method': 'hist',
        'device': 'cpu'
    }
    
    model = XGBRegressor(**xgb_params)
    
    # 训练模型
    model.fit(X_train_safe, y_train)
    
    # 交叉验证
    cv_scores = cross_val_score(model, X_train_safe, y_train, cv=5, scoring='r2')
    
    # 预测
    y_train_pred = model.predict(X_train_safe)
    y_test_pred = model.predict(X_test_safe)
    
    # 评估
    train_r2 = r2_score(y_train, y_train_pred)
    test_r2 = r2_score(y_test, y_test_pred)
    test_rmse = np.sqrt(mean_squared_error(y_test, y_test_pred))
    test_mae = mean_absolute_error(y_test, y_test_pred)
    
    # 计算MAPE
    non_zero_mask = y_test > 1e-6
    if non_zero_mask.sum() > 0:
        mape = np.mean(np.abs((y_test[non_zero_mask] - y_test_pred[non_zero_mask]) / y_test[non_zero_mask])) * 100
    else:
        mape = float('inf')
    
    print("\n📊 模型性能 (修复后):")
    print(f"  - 训练集 R²: {train_r2:.4f}")
    print(f"  - 测试集 R²: {test_r2:.4f}")
    print(f"  - 交叉验证 R²: {cv_scores.mean():.4f} ± {cv_scores.std() * 2:.4f}")
    print(f"  - 测试集 RMSE: {test_rmse:.2f}")
    print(f"  - 测试集 MAE: {test_mae:.2f}")
    print(f"  - 测试集 MAPE: {mape:.2f}%")
    
    # === 特征重要性分析 ===
    print("\n📈 特征重要性分析 (Top 15):")
    feature_importance = pd.DataFrame({
        'feature': safe_features,
        'importance': model.feature_importances_
    }).sort_values('importance', ascending=False)
    
    for idx, row in feature_importance.head(15).iterrows():
        print(f"  {idx+1:2d}. {row['feature']:30s} {row['importance']:.6f}")
    
    # === 保存修复后的模型 ===
    print("\n💾 保存修复后的模型...")
    
    results_dir = "/home/<USER>/cem208/code/ie2025/results/total_demand_fixed"
    os.makedirs(results_dir, exist_ok=True)
    
    # 保存模型
    model_path = os.path.join(results_dir, "xgboost_total_demand_fixed.joblib")
    joblib.dump(model, model_path)
    
    # 保存结果
    import json
    from datetime import datetime
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results = {
        'timestamp': timestamp,
        'model_type': 'XGBoost_Fixed',
        'prediction_target': 'total_demand',
        'leakage_features_removed': features_to_remove,
        'safe_features_count': len(safe_features),
        'model_performance': {
            'train_r2': float(train_r2),
            'test_r2': float(test_r2),
            'cv_r2_mean': float(cv_scores.mean()),
            'cv_r2_std': float(cv_scores.std()),
            'test_rmse': float(test_rmse),
            'test_mae': float(test_mae),
            'test_mape': float(mape)
        },
        'model_parameters': xgb_params,
        'top_features': feature_importance.head(15).to_dict('records')
    }
    
    results_path = os.path.join(results_dir, f"training_results_fixed_{timestamp}.json")
    with open(results_path, 'w') as f:
        json.dump(results, f, indent=2)
    
    # 保存特征重要性
    importance_path = os.path.join(results_dir, f"feature_importance_fixed_{timestamp}.csv")
    feature_importance.to_csv(importance_path, index=False)
    
    print(f"✅ 修复后的模型已保存到: {results_dir}")
    print(f"  - 模型文件: {model_path}")
    print(f"  - 结果文件: {results_path}")
    print(f"  - 特征重要性: {importance_path}")
    
    # === 性能对比 ===
    print("\n🔄 性能对比:")
    print("  修复前 (有泄漏):")
    print("    - 测试集 R²: 0.9103")
    print("    - 测试集 RMSE: 18.67")
    print("    - 测试集 MAE: 12.84")
    print("  修复后 (无泄漏):")
    print(f"    - 测试集 R²: {test_r2:.4f}")
    print(f"    - 测试集 RMSE: {test_rmse:.2f}")
    print(f"    - 测试集 MAE: {test_mae:.2f}")
    
    performance_change = {
        'r2_change': test_r2 - 0.9103,
        'rmse_change': test_rmse - 18.67,
        'mae_change': test_mae - 12.84
    }
    
    print("\n📊 性能变化:")
    print(f"  - R² 变化: {performance_change['r2_change']:+.4f}")
    print(f"  - RMSE 变化: {performance_change['rmse_change']:+.2f}")
    print(f"  - MAE 变化: {performance_change['mae_change']:+.2f}")
    
    if performance_change['r2_change'] < -0.1:
        print("⚠️  性能明显下降，说明原模型确实存在泄漏问题")
    else:
        print("✅ 性能变化在可接受范围内，模型仍然有效")
    
    print("\n🛡️ 标签泄漏修复完成!")
    return model, results

if __name__ == "__main__":
    model, results = train_fixed_total_demand_model()