"""
XGBoost Model for Demand Prediction
Implements XGBoost regression model for airline demand forecasting
"""
import pandas as pd
import numpy as np
from xgboost import XGBRegressor
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.model_selection import GridSearchCV, cross_val_score
import joblib
import logging
from typing import Dict, Tuple, Optional

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class XGBoostDemandPredictor:
    """
    XGBoost model for airline demand prediction
    """
    
    def __init__(self):
        """
        Initialize XGBoost model with default parameters
        """
        self.model = None
        self.is_trained = False
        self.feature_importance = None
        
    def initialize_model(self, **kwargs):
        """
        Initialize XGBoost model with custom parameters
        
        Args:
            **kwargs: XGBoost parameters
        """
        default_params = {
            'n_estimators': 100,
            'max_depth': 6,
            'learning_rate': 0.1,
            'random_state': 42,
            'n_jobs': -1
        }
        
        # Update with custom parameters
        default_params.update(kwargs)
        
        self.model = XGBRegressor(**default_params)
        logger.info("XGBoost model initialized with parameters")
        
    def train(self, X_train: pd.DataFrame, y_train: pd.Series) -> Dict:
        """
        Train the XGBoost model
        
        Args:
            X_train (pd.DataFrame): Training features
            y_train (pd.Series): Training target
            
        Returns:
            Dict: Training results and metrics
        """
        if self.model is None:
            self.initialize_model()
            
        logger.info("Training XGBoost model")
        
        # Train the model
        self.model.fit(X_train, y_train)
        self.is_trained = True
        
        # Get feature importance
        self.feature_importance = pd.DataFrame({
            'feature': X_train.columns,
            'importance': self.model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        # Cross-validation score
        cv_scores = cross_val_score(self.model, X_train, y_train, cv=5, scoring='r2')
        
        results = {
            'training_complete': True,
            'cv_mean_r2': cv_scores.mean(),
            'cv_std_r2': cv_scores.std(),
            'feature_importance': self.feature_importance
        }
        
        logger.info(f"Model trained. CV R²: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")
        return results
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        Make predictions using the trained model
        
        Args:
            X (pd.DataFrame): Features for prediction
            
        Returns:
            np.ndarray: Predictions
        """
        if not self.is_trained:
            raise ValueError("Model not trained yet. Call train() first.")
            
        predictions = self.model.predict(X)
        logger.info(f"Made predictions for {len(predictions)} samples")
        return predictions
    
    def evaluate(self, X_test: pd.DataFrame, y_test: pd.Series) -> Dict:
        """
        Evaluate model performance on test data
        
        Args:
            X_test (pd.DataFrame): Test features
            y_test (pd.Series): Test target
            
        Returns:
            Dict: Evaluation metrics
        """
        if not self.is_trained:
            raise ValueError("Model not trained yet. Call train() first.")
            
        # Make predictions
        y_pred = self.predict(X_test)
        
        # Calculate metrics
        r2 = r2_score(y_test, y_pred)
        mse = mean_squared_error(y_test, y_pred)
        mae = mean_absolute_error(y_test, y_pred)
        rmse = np.sqrt(mse)
        
        # Calculate additional metrics - improved MAPE calculation
        # Only calculate MAPE for non-zero actual values to avoid division issues
        non_zero_mask = y_test > 1e-6  # Use a reasonable threshold
        if non_zero_mask.sum() > 0:
            mape = np.mean(np.abs((y_test[non_zero_mask] - y_pred[non_zero_mask]) / y_test[non_zero_mask])) * 100
        else:
            mape = float('inf')  # If all values are zero, MAPE is undefined
        
        metrics = {
            'r2_score': r2,
            'mse': mse,
            'mae': mae,
            'rmse': rmse,
            'mape': mape
        }
        
        logger.info(f"Evaluation results - R²: {r2:.4f}, RMSE: {rmse:.2f}, MAE: {mae:.2f}")
        return metrics
    
    def hyperparameter_tuning(self, X_train: pd.DataFrame, y_train: pd.Series) -> Dict:
        """
        Perform hyperparameter tuning using grid search
        
        Args:
            X_train (pd.DataFrame): Training features
            y_train (pd.Series): Training target
            
        Returns:
            Dict: Best parameters and scores
        """
        logger.info("Starting hyperparameter tuning")
        
        # Define parameter grid
        param_grid = {
            'n_estimators': [50, 100, 200],
            'max_depth': [3, 6, 9],
            'learning_rate': [0.01, 0.1, 0.2],
            'subsample': [0.8, 1.0]
        }
        
        # Initialize base model
        base_model = XGBRegressor(random_state=42, n_jobs=-1)
        
        # Perform grid search
        grid_search = GridSearchCV(
            base_model, 
            param_grid, 
            cv=3,  # Reduced CV for faster tuning
            scoring='r2',
            n_jobs=-1,
            verbose=1
        )
        
        grid_search.fit(X_train, y_train)
        
        # Update model with best parameters
        self.model = grid_search.best_estimator_
        
        results = {
            'best_params': grid_search.best_params_,
            'best_score': grid_search.best_score_,
            'cv_results': grid_search.cv_results_
        }
        
        logger.info(f"Best parameters: {grid_search.best_params_}")
        logger.info(f"Best CV score: {grid_search.best_score_:.4f}")
        
        return results
    
    def save_model(self, filepath: str):
        """
        Save trained model to file
        
        Args:
            filepath (str): Path to save model
        """
        if not self.is_trained:
            raise ValueError("Model not trained yet. Call train() first.")
            
        joblib.dump(self.model, filepath)
        logger.info(f"Model saved to {filepath}")
    
    def load_model(self, filepath: str):
        """
        Load trained model from file
        
        Args:
            filepath (str): Path to load model
        """
        self.model = joblib.load(filepath)
        self.is_trained = True
        logger.info(f"Model loaded from {filepath}")

def train_xgboost_model(dataset: Dict, hyperparameter_tuning: bool = False) -> Tuple[XGBoostDemandPredictor, Dict]:
    """
    Train XGBoost model with the prepared dataset
    
    Args:
        dataset (Dict): Prepared dataset from data_preparation
        hyperparameter_tuning (bool): Whether to perform hyperparameter tuning
        
    Returns:
        Tuple: (trained_model, training_results)
    """
    logger.info("Starting XGBoost model training")
    
    # Initialize model
    model = XGBoostDemandPredictor()
    
    # Perform hyperparameter tuning if requested
    if hyperparameter_tuning:
        tuning_results = model.hyperparameter_tuning(dataset['X_train'], dataset['y_train'])
        logger.info("Hyperparameter tuning completed")
    else:
        model.initialize_model()
    
    # Train model
    training_results = model.train(dataset['X_train'], dataset['y_train'])
    
    # Evaluate on test set
    test_metrics = model.evaluate(dataset['X_test'], dataset['y_test'])
    training_results['test_metrics'] = test_metrics
    
    logger.info("XGBoost model training completed")
    return model, training_results

if __name__ == "__main__":
    # Test XGBoost model
    print("XGBoost Demand Prediction Model")
    print("This module implements XGBoost regression for airline demand forecasting.")