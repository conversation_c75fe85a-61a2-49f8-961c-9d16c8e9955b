"""
LSTM/GRU Training for Booking Curve Prediction
Implements deep learning models for time series booking curve forecasting
"""
import pandas as pd
import numpy as np
import os
import json
import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Try to import TensorFlow with GPU support
try:
    # Enable GPU memory growth to avoid OOM errors
    gpus = tf.config.experimental.list_physical_devices('GPU')
    if gpus:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print(f"Found {len(gpus)} GPU(s): {[gpu.name for gpu in gpus]}")
        print("TensorFlow configured to use GPU acceleration")
    else:
        print("No GPUs found, using CPU")
        
    import tensorflow as tf
    from tensorflow.keras.models import Sequential, Model
    from tensorflow.keras.layers import LSTM, GRU, Dense, Dropout, Input, Concatenate
    from tensorflow.keras.optimizers import <PERSON>
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint
    from tensorflow.keras.utils import plot_model
    TENSORFLOW_AVAILABLE = True
    print("TensorFlow loaded successfully with GPU support")
except ImportError:
    TENSORFLOW_AVAILABLE = False
    print("TensorFlow not available")
except Exception as e:
    TENSORFLOW_AVAILABLE = False
    print(f"TensorFlow failed to load: {e}")

from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.preprocessing import StandardScaler
import joblib
import matplotlib.pyplot as plt

# Import our LSTM model
from lstm_model import LSTMDemandPredictor, prepare_lstm_dataset, train_lstm_model

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BookingCurveLSTMTrainer:
    """
    Comprehensive LSTM/GRU trainer for booking curve prediction
    """
    
    def __init__(self, data_root: str = "data/processed/booking_curve"):
        """
        Initialize trainer
        
        Args:
            data_root (str): Root directory for booking curve data
        """
        if not TENSORFLOW_AVAILABLE:
            raise ImportError("TensorFlow is required for LSTM/GRU models")
            
        self.data_root = data_root
        self.models = {}
        self.results = {}
        self.scalers = {}
        
        # Prediction types
        self.prediction_types = ['full_curve', 'window_aggregate', 'key_points', 'final_demand']
        
        # Model architectures to try
        self.model_configs = {
            'lstm_small': {
                'type': 'LSTM',
                'units': [32, 16],
                'dropout': 0.2,
                'dense': [16, 8],
                'learning_rate': 0.001
            },
            'lstm_medium': {
                'type': 'LSTM',
                'units': [64, 32],
                'dropout': 0.3,
                'dense': [32, 16],
                'learning_rate': 0.001
            },
            'gru_medium': {
                'type': 'GRU',
                'units': [64, 32],
                'dropout': 0.3,
                'dense': [32, 16],
                'learning_rate': 0.001
            }
        }
    
    def load_booking_curve_data(self, prediction_type: str) -> Dict[str, np.ndarray]:
        """
        Load booking curve data for specific prediction type
        
        Args:
            prediction_type (str): Type of prediction ('full_curve', 'window_aggregate', etc.)
            
        Returns:
            Dict: Loaded data arrays
        """
        data_path = os.path.join(self.data_root, prediction_type)
        
        # Load numpy arrays with proper naming pattern
        X_train = np.load(os.path.join(data_path, f'{prediction_type}_X_train.npy'))
        X_test = np.load(os.path.join(data_path, f'{prediction_type}_X_test.npy'))
        y_train = np.load(os.path.join(data_path, f'{prediction_type}_y_train.npy'))
        y_test = np.load(os.path.join(data_path, f'{prediction_type}_y_test.npy'))
        
        # Load feature names
        feature_names_path = os.path.join(data_path, f'{prediction_type}_feature_names.json')
        if os.path.exists(feature_names_path):
            with open(feature_names_path, 'r') as f:
                feature_names = json.load(f)
        else:
            # Try alternative naming
            feature_names_path = os.path.join(data_path, 'feature_names.json')
            if os.path.exists(feature_names_path):
                with open(feature_names_path, 'r') as f:
                    feature_names = json.load(f)
            else:
                # Use generic feature names
                feature_names = [f'feature_{i}' for i in range(X_train.shape[1])]
        
        # Load metadata
        metadata_path = os.path.join(data_path, f'{prediction_type}_metadata.json')
        if os.path.exists(metadata_path):
            with open(metadata_path, 'r') as f:
                metadata = json.load(f)
        else:
            metadata = {'prediction_type': prediction_type}
        
        logger.info(f"Loaded {prediction_type} data:")
        logger.info(f"  - Training samples: {X_train.shape[0]}")
        logger.info(f"  - Test samples: {X_test.shape[0]}")
        logger.info(f"  - Input features: {X_train.shape[1]}")
        logger.info(f"  - Output dimensions: {y_train.shape[1]}")
        
        return {
            'X_train': X_train,
            'X_test': X_test,
            'y_train': y_train,
            'y_test': y_test,
            'feature_names': feature_names,
            'metadata': metadata
        }
    
    def preprocess_for_lstm(self, data: Dict, sequence_length: int = 1) -> Dict:
        """
        Preprocess data for LSTM training
        
        Args:
            data (Dict): Raw data dictionary
            sequence_length (int): Sequence length for LSTM
            
        Returns:
            Dict: Preprocessed data ready for LSTM
        """
        logger.info(f"Preprocessing data for LSTM with sequence length {sequence_length}")
        
        # For sequence-based predictions, we need to reshape the data
        X_train = data['X_train']
        X_test = data['X_test']
        y_train = data['y_train']
        y_test = data['y_test']
        
        # If sequence_length > 1, create sequences
        if sequence_length > 1:
            X_train_seq = []
            y_train_seq = []
            X_test_seq = []
            y_test_seq = []
            
            # Create training sequences
            for i in range(sequence_length - 1, len(X_train)):
                X_train_seq.append(X_train[i-sequence_length+1:i+1])
                y_train_seq.append(y_train[i])
            
            # Create test sequences
            for i in range(sequence_length - 1, len(X_test)):
                X_test_seq.append(X_test[i-sequence_length+1:i+1])
                y_test_seq.append(y_test[i])
            
            X_train = np.array(X_train_seq)
            y_train = np.array(y_train_seq)
            X_test = np.array(X_test_seq)
            y_test = np.array(y_test_seq)
        else:
            # For single step, just add a time dimension
            X_train = X_train.reshape(X_train.shape[0], 1, X_train.shape[1])
            X_test = X_test.reshape(X_test.shape[0], 1, X_test.shape[1])
        
        # Create validation split
        val_size = int(0.2 * len(X_train))
        X_val = X_train[-val_size:]
        y_val = y_train[-val_size:]
        X_train = X_train[:-val_size]
        y_train = y_train[:-val_size]
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train.reshape(-1, X_train.shape[-1]))
        X_val_scaled = scaler.transform(X_val.reshape(-1, X_val.shape[-1]))
        X_test_scaled = scaler.transform(X_test.reshape(-1, X_test.shape[-1]))
        
        # Reshape back
        X_train = X_train_scaled.reshape(X_train.shape)
        X_val = X_val_scaled.reshape(X_val.shape)
        X_test = X_test_scaled.reshape(X_test.shape)
        
        processed_data = {
            'X_train': X_train,
            'X_val': X_val,
            'X_test': X_test,
            'y_train': y_train,
            'y_val': y_val,
            'y_test': y_test,
            'feature_names': data['feature_names'],
            'metadata': data['metadata'],
            'sequence_length': sequence_length,
            'scaler': scaler
        }
        
        logger.info(f"Preprocessed data shapes:")
        logger.info(f"  - X_train: {X_train.shape}")
        logger.info(f"  - X_val: {X_val.shape}")
        logger.info(f"  - X_test: {X_test.shape}")
        logger.info(f"  - y_train: {y_train.shape}")
        logger.info(f"  - y_val: {y_val.shape}")
        logger.info(f"  - y_test: {y_test.shape}")
        
        return processed_data
    
    def build_multi_output_model(self, input_shape: Tuple[int, int], 
                               output_dim: int, config: Dict[str, Any]) -> tf.keras.Model:
        """
        Build LSTM/GRU model for multi-output prediction with GPU acceleration
        
        Args:
            input_shape (Tuple): Input shape (timesteps, features)
            output_dim (int): Output dimension
            config (Dict): Model configuration
            
        Returns:
            tf.keras.Model: Built model
        """
        # Create mixed precision policy for better GPU performance
        policy = tf.keras.mixed_precision.Policy('mixed_float16')
        tf.keras.mixed_precision.set_global_policy(policy)
        
        model = Sequential()
        
        # Add LSTM/GRU layers with optimized settings for GPU
        for i, units in enumerate(config['units']):
            return_sequences = (i < len(config['units']) - 1)
            
            if config['type'] == 'LSTM':
                # Use cuDNN-optimized LSTM for better GPU performance
                model.add(LSTM(units, 
                            return_sequences=return_sequences,
                            input_shape=input_shape if i == 0 else None,
                            activation='tanh',  # cuDNN optimized
                            recurrent_activation='sigmoid',  # cuDNN optimized
                            use_bias=True,
                            unroll=False))  # Let GPU handle sequencing
            else:  # GRU
                model.add(GRU(units, 
                            return_sequences=return_sequences,
                            input_shape=input_shape if i == 0 else None,
                            activation='tanh',  # cuDNN optimized
                            recurrent_activation='sigmoid',  # cuDNN optimized
                            use_bias=True,
                            unroll=False))  # Let GPU handle sequencing
            
            if return_sequences:
                model.add(Dropout(config['dropout']))
        
        # Add dense layers
        for units in config['dense']:
            model.add(Dense(units, activation='relu'))
            if config['dropout'] > 0:
                model.add(Dropout(config['dropout']))
        
        # Output layer with float32 precision for numerical stability
        model.add(Dense(output_dim, dtype='float32'))
        
        # Compile model with GPU-optimized settings
        optimizer = Adam(learning_rate=config['learning_rate'])
        model.compile(optimizer=optimizer, 
                     loss='mse', 
                     metrics=['mae'])
        
        logger.info(f"Built {config['type']} model with GPU optimization")
        return model
    
    def train_model_for_type(self, prediction_type: str, model_config_name: str = 'lstm_medium',
                            sequence_length: int = 1, epochs: int = 100) -> Tuple[Dict[str, Any], tf.keras.Model]:
        """
        Train model for specific prediction type
        
        Args:
            prediction_type (str): Type of prediction
            model_config_name (str): Model configuration name
            sequence_length (int): Sequence length
            epochs (int): Number of training epochs
            
        Returns:
            Tuple: (results, model)
        """
        logger.info(f"Training {model_config_name} model for {prediction_type}")
        
        # Load data
        data = self.load_booking_curve_data(prediction_type)
        
        # Preprocess for LSTM
        processed_data = self.preprocess_for_lstm(data, sequence_length)
        
        # Get model configuration
        config = self.model_configs[model_config_name]
        
        # Build model
        input_shape = (sequence_length, len(processed_data['feature_names']))
        output_dim = processed_data['y_train'].shape[1]
        
        model = self.build_multi_output_model(input_shape, output_dim, config)
        
        # Callbacks
        callbacks = [
            EarlyStopping(patience=15, restore_best_weights=True),
            ReduceLROnPlateau(patience=8, factor=0.5, min_lr=1e-7)
        ]
        
        # Train model with GPU-optimized batch size
        # Use larger batch size for GPU efficiency
        gpu_batch_size = min(128, len(processed_data['X_train']) // 10)  # Adaptive batch size
        
        logger.info(f"Using GPU-optimized batch size: {gpu_batch_size}")
        
        history = model.fit(
            processed_data['X_train'], processed_data['y_train'],
            validation_data=(processed_data['X_val'], processed_data['y_val']),
            epochs=epochs,
            batch_size=gpu_batch_size,
            callbacks=callbacks,
            verbose=1
        )
        
        # Evaluate model
        test_metrics = self.evaluate_model(model, processed_data)
        
        # Store results
        results = {
            'prediction_type': prediction_type,
            'model_config': model_config_name,
            'sequence_length': sequence_length,
            'training_history': {
                'loss': history.history['loss'],
                'val_loss': history.history['val_loss'],
                'mae': history.history['mae'],
                'val_mae': history.history['val_mae']
            },
            'test_metrics': test_metrics,
            'model_summary': self.get_model_summary(model),
            'data_info': {
                'input_features': len(processed_data['feature_names']),
                'output_dimensions': output_dim,
                'train_samples': len(processed_data['X_train']),
                'val_samples': len(processed_data['X_val']),
                'test_samples': len(processed_data['X_test'])
            },
            'feature_names': processed_data['feature_names'],
            'metadata': processed_data['metadata'],
            'training_timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"Training completed for {prediction_type}")
        logger.info(f"Test R²: {test_metrics['r2_mean']:.4f}")
        logger.info(f"Test RMSE: {test_metrics['rmse_mean']:.4f}")
        
        return results, model
    
    def evaluate_model(self, model: tf.keras.Model, data: Dict[str, np.ndarray]) -> Dict[str, float]:
        """
        Evaluate model performance
        
        Args:
            model (tf.keras.Model): Trained model
            data (Dict): Test data
            
        Returns:
            Dict: Evaluation metrics
        """
        # Make predictions
        y_pred = model.predict(data['X_test'], verbose=0)
        y_true = data['y_test']
        
        # Calculate metrics for each output dimension
        r2_scores = []
        mse_scores = []
        mae_scores = []
        rmse_scores = []
        mape_scores = []
        
        for i in range(y_true.shape[1]):
            r2 = r2_score(y_true[:, i], y_pred[:, i])
            mse = mean_squared_error(y_true[:, i], y_pred[:, i])
            mae = mean_absolute_error(y_true[:, i], y_pred[:, i])
            rmse = np.sqrt(mse)
            
            # Calculate MAPE
            non_zero_mask = y_true[:, i] > 1e-6
            if non_zero_mask.sum() > 0:
                mape = np.mean(np.abs((y_true[non_zero_mask, i] - y_pred[non_zero_mask, i]) / y_true[non_zero_mask, i])) * 100
            else:
                mape = float('inf')
            
            r2_scores.append(r2)
            mse_scores.append(mse)
            mae_scores.append(mae)
            rmse_scores.append(rmse)
            mape_scores.append(mape)
        
        # Calculate mean metrics
        metrics = {
            'r2_mean': np.mean(r2_scores),
            'r2_std': np.std(r2_scores),
            'mse_mean': np.mean(mse_scores),
            'mse_std': np.std(mse_scores),
            'mae_mean': np.mean(mae_scores),
            'mae_std': np.std(mae_scores),
            'rmse_mean': np.mean(rmse_scores),
            'rmse_std': np.std(rmse_scores),
            'mape_mean': np.mean(mape_scores),
            'mape_std': np.std(mape_scores),
            'individual_r2': r2_scores,
            'individual_rmse': rmse_scores
        }
        
        return metrics
    
    def get_model_summary(self, model: tf.keras.Model) -> str:
        """
        Get model summary as string
        
        Args:
            model (tf.keras.Model): Model to summarize
            
        Returns:
            str: Model summary
        """
        import io
        stream = io.StringIO()
        model.summary(print_fn=lambda x: stream.write(x + '\n'))
        return stream.getvalue()
    
    def train_all_models(self, epochs: int = 50) -> Dict[str, Dict[str, Any]]:
        """
        Train models for all prediction types
        
        Args:
            epochs (int): Number of training epochs
            
        Returns:
            Dict: All results
        """
        logger.info("Starting training for all prediction types")
        
        all_results = {}
        
        # Train for each prediction type
        for prediction_type in self.prediction_types:
            logger.info(f"Training models for {prediction_type}")
            
            type_results = {}
            
            # Try different model configurations
            for config_name in ['lstm_medium', 'gru_medium']:  # Focus on medium models
                try:
                    results, model = self.train_model_for_type(
                        prediction_type, 
                        model_config_name=config_name,
                        sequence_length=1,  # Start with single step
                        epochs=epochs
                    )
                    
                    type_results[config_name] = {
                        'results': results,
                        'model': model
                    }
                    
                    # Save model
                    self.save_model(model, prediction_type, config_name, results)
                    
                except Exception as e:
                    logger.error(f"Failed to train {config_name} for {prediction_type}: {e}")
                    type_results[config_name] = {'error': str(e)}
            
            all_results[prediction_type] = type_results
        
        # Save summary results
        self.save_all_results(all_results)
        
        logger.info("Training completed for all prediction types")
        return all_results
    
    def save_model(self, model: tf.keras.Model, prediction_type: str, 
                   config_name: str, results: Dict[str, Any]):
        """
        Save trained model
        
        Args:
            model (tf.keras.Model): Trained model
            prediction_type (str): Prediction type
            config_name (str): Configuration name
            results (Dict): Training results
        """
        # Create results directory
        results_dir = "results/booking_curve_lstm"
        os.makedirs(results_dir, exist_ok=True)
        
        # Save model
        model_path = os.path.join(results_dir, f"{prediction_type}_{config_name}.keras")
        model.save(model_path)
        
        # Save results
        results_path = os.path.join(results_dir, f"{prediction_type}_{config_name}_results.json")
        
        # Convert numpy arrays to lists for JSON serialization
        json_results = self.prepare_json_for_saving(results)
        
        with open(results_path, 'w') as f:
            json.dump(json_results, f, indent=2)
        
        logger.info(f"Model and results saved for {prediction_type}_{config_name}")
    
    def prepare_json_for_saving(self, obj: Any) -> Any:
        """
        Prepare object for JSON serialization
        
        Args:
            obj (Any): Object to prepare
            
        Returns:
            Any: JSON-serializable object
        """
        if isinstance(obj, dict):
            return {k: self.prepare_json_for_saving(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self.prepare_json_for_saving(item) for item in obj]
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        else:
            return obj
    
    def save_all_results(self, all_results: Dict[str, Dict[str, Any]]):
        """
        Save all training results summary
        
        Args:
            all_results (Dict): All results dictionary
        """
        results_dir = "results/booking_curve_lstm"
        os.makedirs(results_dir, exist_ok=True)
        
        # Create summary report
        summary_report = self.create_summary_report(all_results)
        
        # Save summary
        summary_path = os.path.join(results_dir, "training_summary.json")
        json_summary = self.prepare_json_for_saving(summary_report)
        
        with open(summary_path, 'w') as f:
            json.dump(json_summary, f, indent=2)
        
        # Save human-readable report
        report_path = os.path.join(results_dir, "training_report.md")
        with open(report_path, 'w') as f:
            f.write(self.generate_markdown_report(summary_report))
        
        logger.info("All results saved with summary report")
    
    def create_summary_report(self, all_results: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Create summary report from all results
        
        Args:
            all_results (Dict): All results
            
        Returns:
            Dict: Summary report
        """
        summary = {
            'training_timestamp': datetime.now().isoformat(),
            'prediction_types': {},
            'best_models': {},
            'performance_comparison': {}
        }
        
        for prediction_type, type_results in all_results.items():
            type_summary = {}
            best_performance = -float('inf')
            best_config = None
            
            for config_name, config_data in type_results.items():
                if 'error' in config_data:
                    type_summary[config_name] = {'error': config_data['error']}
                    continue
                
                test_metrics = config_data['results']['test_metrics']
                
                config_summary = {
                    'r2_score': test_metrics['r2_mean'],
                    'rmse': test_metrics['rmse_mean'],
                    'mae': test_metrics['mae_mean'],
                    'mape': test_metrics['mape_mean'],
                    'training_epochs': len(config_data['results']['training_history']['loss'])
                }
                
                type_summary[config_name] = config_summary
                
                # Track best performing model
                if test_metrics['r2_mean'] > best_performance:
                    best_performance = test_metrics['r2_mean']
                    best_config = config_name
            
            summary['prediction_types'][prediction_type] = type_summary
            summary['best_models'][prediction_type] = {
                'config': best_config,
                'r2_score': best_performance if best_config else None
            }
        
        return summary
    
    def generate_markdown_report(self, summary: Dict[str, Any]) -> str:
        """
        Generate markdown report from summary
        
        Args:
            summary (Dict): Summary data
            
        Returns:
            str: Markdown report
        """
        report = f"""# LSTM/GRU Booking Curve Prediction Training Report

**Training Date**: {summary['training_timestamp']}

## Overview

This report summarizes the performance of LSTM/GRU models for 4 types of booking curve prediction tasks.

## Best Models Summary

| Prediction Type | Best Configuration | R² Score | RMSE |
|-----------------|------------------|----------|------|
"""
        
        for pred_type, best_info in summary['best_models'].items():
            if best_info['r2_score'] is not None:
                pred_type_name = pred_type.replace('_', ' ').title()
                config_name = best_info['config'].replace('_', ' ').title()
                r2_score = f"{best_info['r2_score']:.4f}"
                
                # Get RMSE from detailed results
                rmse = "N/A"
                if pred_type in summary['prediction_types'] and best_info['config'] in summary['prediction_types'][pred_type]:
                    if 'rmse' in summary['prediction_types'][pred_type][best_info['config']]:
                        rmse = f"{summary['prediction_types'][pred_type][best_info['config']]['rmse']:.4f}"
                
                report += f"| {pred_type_name} | {config_name} | {r2_score} | {rmse} |\n"
        
        report += "\n## Detailed Performance by Prediction Type\n\n"
        
        for pred_type, type_results in summary['prediction_types'].items():
            pred_type_name = pred_type.replace('_', ' ').title()
            report += f"### {pred_type_name}\n\n"
            report += "| Configuration | R² Score | RMSE | MAE | MAPE | Epochs |\n"
            report += "|---------------|----------|------|-----|------|--------|\n"
            
            for config_name, config_summary in type_results.items():
                if 'error' in config_summary:
                    report += f"| {config_name.replace('_', ' ').title()} | Error | N/A | N/A | N/A | N/A |\n"
                else:
                    r2 = f"{config_summary.get('r2_score', 0):.4f}"
                    rmse = f"{config_summary.get('rmse', 0):.4f}"
                    mae = f"{config_summary.get('mae', 0):.4f}"
                    mape = f"{config_summary.get('mape', 0):.2f}%"
                    epochs = config_summary.get('training_epochs', 0)
                    
                    report += f"| {config_name.replace('_', ' ').title()} | {r2} | {rmse} | {mae} | {mape} | {epochs} |\n"
            
            report += "\n"
        
        report += """## Conclusions

- Training completed for all 4 prediction types
- Performance varies by prediction complexity
- Full curve prediction is most challenging due to high output dimensionality
- Models are saved in `results/booking_curve_lstm/` directory
"""
        
        return report

def main():
    """
    Main function to run LSTM/GRU training
    """
    print("=" * 60)
    print("LSTM/GRU Booking Curve Prediction Training")
    print("=" * 60)
    
    # Initialize trainer
    trainer = BookingCurveLSTMTrainer()
    
    # Train all models
    print("\nStarting training for all prediction types...")
    all_results = trainer.train_all_models(epochs=50)  # Reduced epochs for faster execution
    
    print("\nTraining completed!")
    print("Results saved in results/booking_curve_lstm/")

if __name__ == "__main__":
    main()