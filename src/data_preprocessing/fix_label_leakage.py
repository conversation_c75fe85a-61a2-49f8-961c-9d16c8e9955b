"""
No-Leakage Feature Engineering for Total Demand Prediction
Removes label leakage issues from the original feature engineering
"""
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, LabelEncoder
from typing import Dict, List, Tuple
import joblib
import os

class NoLeakageFeatureEngineer:
    """
    无标签泄漏的特征工程类
    只使用预测时可获得的基础特征，避免使用任何RD相关统计
    """
    
    def __init__(self):
        self.scaler = StandardScaler()
        self.label_encoders = {}
        self.feature_columns = []
        
    def create_safe_features(self, schedule_data: pd.DataFrame, 
                           fleet_data: pd.DataFrame, 
                           market_share_data: pd.DataFrame) -> pd.DataFrame:
        """
        创建安全的无泄漏特征
        
        只使用以下安全特征来源：
        1. 航班时刻表数据 (schedule_data) - 基础航班信息
        2. 机队数据 (fleet_data) - 机型和座位配置
        3. 市场份额数据 (market_share_data) - 市场环境
        
        完全不使用products数据中的RD相关统计
        """
        print("🛡️ 创建无泄漏特征集...")
        
        # 识别枢纽机场
        hub_airports = self._identify_hub_airports(schedule_data)
        
        features = []
        
        for _, flight in schedule_data.iterrows():
            flight_features = self._extract_safe_flight_features(
                flight, fleet_data, market_share_data, hub_airports
            )
            features.append(flight_features)
        
        feature_df = pd.DataFrame(features)
        print(f"✅ 创建了 {feature_df.shape[0]} 个航班的无泄漏特征")
        print(f"📊 特征维度: {feature_df.shape[1]} 个特征")
        
        return feature_df
    
    def _identify_hub_airports(self, schedule_data: pd.DataFrame) -> List[str]:
        """识别枢纽机场"""
        departure_counts = schedule_data['origin'].value_counts()
        arrival_counts = schedule_data['destination'].value_counts()
        total_counts = departure_counts.add(arrival_counts, fill_value=0)
        hub_airports = total_counts.nlargest(10).index.tolist()
        print(f"🏆 识别到枢纽机场: {hub_airports}")
        return hub_airports
    
    def _extract_safe_flight_features(self, flight: pd.Series, 
                                    fleet_data: pd.DataFrame, 
                                    market_share_data: pd.DataFrame,
                                    hub_airports: List[str]) -> Dict[str, float]:
        """
        提取单个航班的安全特征（无泄漏）
        """
        origin = flight['origin']
        destination = flight['destination']
        route = f"{origin}-{destination}"
        
        # === 基础时间特征 ===
        features = {}
        
        # 起降时间
        features['departure_time'] = flight['depoff']
        features['arrival_time'] = flight['arroff']
        features['departure_hour'] = flight['deptime'] % 24
        features['arrival_hour'] = flight['arrtime'] % 24
        
        # 航班时长和时区 (需要计算或者使用现有列)
        # 简化处理，使用起降时间差作为航班时长估算
        features['flight_duration'] = flight['arroff'] - flight['depoff']
        features['dep_offset'] = flight['depoff']  # 简化处理，直接使用偏移值
        features['arr_offset'] = flight['arroff']  # 简化处理，直接使用偏移值
        features['time_offset_magnitude'] = abs(features['dep_offset']) + abs(features['arr_offset'])
        
        # 出发时间段
        dep_hour = features['departure_hour']
        if 6 <= dep_hour <= 11:
            features['departure_period'] = 'morning'
        elif 12 <= dep_hour <= 17:
            features['departure_period'] = 'afternoon'
        elif 18 <= dep_hour <= 22:
            features['departure_period'] = 'evening'
        else:
            features['departure_period'] = 'night'
        
        # 到达时间段
        arr_hour = features['arrival_hour']
        if 6 <= arr_hour <= 11:
            features['arrival_period'] = 'morning'
        elif 12 <= arr_hour <= 17:
            features['arrival_period'] = 'afternoon'
        elif 18 <= arr_hour <= 22:
            features['arrival_period'] = 'evening'
        else:
            features['arrival_period'] = 'night'
        
        # === 机场和航线特征 ===
        features['route'] = route
        features['is_hub_origin'] = 1 if origin in hub_airports else 0
        features['is_hub_destination'] = 1 if destination in hub_airports else 0
        features['is_hub_to_hub'] = 1 if (origin in hub_airports and destination in hub_airports) else 0
        features['same_airport'] = 1 if origin == destination else 0
        features['is_multi_stop'] = 1 if len(route.split('-')) > 2 else 0
        
        # === 市场竞争特征 ===
        market_share_row = market_share_data[
            (market_share_data['Org'] == origin) & 
            (market_share_data['Des'] == destination)
        ]
        
        market_share = market_share_row['Host_share'].iloc[0] if len(market_share_row) > 0 else 0.5
        features['market_share'] = market_share
        
        # 市场竞争等级
        if market_share >= 0.8:
            features['competition_level'] = 'monopoly'
        elif market_share >= 0.5:
            features['competition_level'] = 'dominant'
        elif market_share >= 0.2:
            features['competition_level'] = 'competitive'
        else:
            features['competition_level'] = 'weak'
        
        features['competition_score'] = market_share
        features['hub_connection_score'] = features['is_hub_origin'] + features['is_hub_destination']
        
        # === 机队特征 ===
        # 获取该航班的飞机类型
        flight_id = flight['flight']
        fleet_row = fleet_data[fleet_data['flight'] == flight_id]
        
        if len(fleet_row) > 0:
            aircraft_type = fleet_row['AircraftType'].iloc[0]
            features['aircraft_type'] = aircraft_type
            features['seat_capacity'] = fleet_row['Seats'].iloc[0]
            
            # 解析机队编码
            fleet_code = fleet_row['fleet'].iloc[0]
            features['F_seats'] = int(str(fleet_code)[0]) if str(fleet_code)[0].isdigit() else 0
            features['C_seats'] = int(str(fleet_code)[1]) if len(str(fleet_code)) > 1 and str(fleet_code)[1].isdigit() else 0
            features['Y_seats'] = int(str(fleet_code)[2]) if len(str(fleet_code)) > 2 and str(fleet_code)[2].isdigit() else 0
        else:
            features['aircraft_type'] = 'unknown'
            features['seat_capacity'] = 120  # 默认值
            features['F_seats'] = 0
            features['C_seats'] = 0
            features['Y_seats'] = 0
        
        features['capacity_ratio'] = features['seat_capacity'] / 120  # 标准化
        
        # === 航线频率特征 ===
        route_stats = schedule_data[
            (schedule_data['origin'] == origin) & 
            (schedule_data['destination'] == destination)
        ]
        features['route_frequency'] = len(route_stats)
        features['seats_per_hour'] = features['seat_capacity'] / max(features['flight_duration'], 1)
        
        return features
    
    def preprocess_features(self, feature_df: pd.DataFrame) -> Tuple[pd.DataFrame, dict]:
        """
        预处理特征：编码和标准化
        """
        print("🔄 预处理特征...")
        
        processed_df = feature_df.copy()
        
        # 分离数值和分类特征
        numeric_features = []
        categorical_features = []
        
        for col in processed_df.columns:
            if col in ['route', 'flight']:  # 跳过高基数特征
                continue
            elif processed_df[col].dtype in ['int64', 'float64']:
                numeric_features.append(col)
            else:
                categorical_features.append(col)
        
        print(f"📈 数值特征: {len(numeric_features)} 个")
        print(f"📊 分类特征: {len(categorical_features)} 个")
        
        # 标准化数值特征
        if numeric_features:
            processed_df[numeric_features] = self.scaler.fit_transform(processed_df[numeric_features])
        
        # 编码分类特征
        for cat_feature in categorical_features:
            if cat_feature not in self.label_encoders:
                self.label_encoders[cat_feature] = LabelEncoder()
                processed_df[f'{cat_feature}_encoded'] = self.label_encoders[cat_feature].fit_transform(processed_df[cat_feature])
            else:
                processed_df[f'{cat_feature}_encoded'] = self.label_encoders[cat_feature].transform(processed_df[cat_feature])
        
        # 移除原始分类特征
        processed_df = processed_df.drop(columns=categorical_features)
        
        # 保存特征列列表
        self.feature_columns = processed_df.columns.tolist()
        
        print(f"✅ 最终特征维度: {processed_df.shape[1]}")
        
        return processed_df
    
    def save_preprocessor(self, filepath: str):
        """保存预处理器"""
        preprocessor_data = {
            'scaler': self.scaler,
            'label_encoders': self.label_encoders,
            'feature_columns': self.feature_columns
        }
        joblib.dump(preprocessor_data, filepath)
        print(f"💾 预处理器已保存到: {filepath}")
    
    def load_preprocessor(self, filepath: str):
        """加载预处理器"""
        preprocessor_data = joblib.load(filepath)
        self.scaler = preprocessor_data['scaler']
        self.label_encoders = preprocessor_data['label_encoders']
        self.feature_columns = preprocessor_data['feature_columns']
        print(f"📂 预处理器已从 {filepath} 加载")


def create_no_leakage_dataset():
    """
    创建无标签泄漏的总需求预测数据集
    """
    print("🛡️ 开始创建无泄漏数据集...")
    
    # 数据路径
    data_path = "/home/<USER>/cem208/code/ie2025/data"
    processed_path = "/home/<USER>/cem208/code/ie2025/data/processed"
    
    # 加载原始数据
    print("📂 加载原始数据...")
    schedule_data = pd.read_csv(os.path.join(data_path, "data_fam_schedule.csv"))
    fleet_data = pd.read_csv(os.path.join(data_path, "data_fam_fleet.csv"))
    market_share_data = pd.read_csv(os.path.join(data_path, "data_fam_market_share.csv"))
    products_data = pd.read_csv(os.path.join(data_path, "data_fam_products.csv"))
    
    print(f"📊 原始数据规模:")
    print(f"  - 航班时刻表: {schedule_data.shape}")
    print(f"  - 机队数据: {fleet_data.shape}")
    print(f"  - 市场份额: {market_share_data.shape}")
    print(f"  - 产品数据: {products_data.shape}")
    
    # 计算总需求标签 (从products数据)
    print("🎯 计算总需求标签...")
    demand_labels = {}
    rd_columns = [col for col in products_data.columns if col.startswith('RD')]
    
    for flight_id in schedule_data['flight'].unique():
        # 在products数据中查找对应的航班 (可能在Flight1, Flight2, Flight3列)
        flight_products = products_data[
            (products_data['Flight1'] == flight_id) | 
            (products_data['Flight2'] == flight_id) | 
            (products_data['Flight3'] == flight_id)
        ]
        if len(flight_products) > 0:
            # 计算该航班的总需求（所有舱位+所有RD点的总和）
            total_demand = flight_products[rd_columns].values.sum()
            demand_labels[flight_id] = total_demand
        else:
            demand_labels[flight_id] = 0
    
    # 创建无泄漏特征
    feature_engineer = NoLeakageFeatureEngineer()
    feature_df = feature_engineer.create_safe_features(
        schedule_data, fleet_data, market_share_data
    )
    
    # 添加航班ID并生成标签
    feature_df['flight'] = schedule_data['flight'].values
    feature_df['total_demand'] = feature_df['flight'].map(demand_labels)
    
    # 检查数据完整性
    print(f"🔍 数据完整性检查:")
    print(f"  - 特征数据行数: {len(feature_df)}")
    print(f"  - 缺失标签数: {feature_df['total_demand'].isnull().sum()}")
    
    # 移除缺失标签的行
    feature_df = feature_df.dropna(subset=['total_demand'])
    
    # 预处理特征
    processed_features = feature_engineer.preprocess_features(
        feature_df.drop(columns=['flight', 'total_demand'])
    )
    
    # 保存处理后的数据
    print("💾 保存处理后的数据...")
    
    # 保存特征数据
    processed_features.to_csv(
        os.path.join(processed_path, "total_demand_X_no_leakage.csv"), 
        index=False
    )
    
    # 保存标签数据
    feature_df['total_demand'].to_csv(
        os.path.join(processed_path, "total_demand_y_no_leakage.csv"), 
        index=False
    )
    
    # 保存预处理器
    feature_engineer.save_preprocessor(
        os.path.join(processed_path, "total_demand_preprocessor_no_leakage.pkl")
    )
    
    # 保存特征列表
    feature_info = pd.DataFrame({
        'feature_name': processed_features.columns.tolist(),
        'feature_type': ['numeric'] * len(processed_features.columns)
    })
    feature_info.to_csv(
        os.path.join(processed_path, "total_demand_features_no_leakage.csv"), 
        index=False
    )
    
    # 数据统计
    print("\n📊 无泄漏数据集统计:")
    print(f"  - 样本数量: {len(processed_features)}")
    print(f"  - 特征数量: {len(processed_features.columns)}")
    print(f"  - 需求范围: {feature_df['total_demand'].min():.2f} - {feature_df['total_demand'].max():.2f}")
    print(f"  - 平均需求: {feature_df['total_demand'].mean():.2f}")
    print(f"  - 需求标准差: {feature_df['total_demand'].std():.2f}")
    
    print("\n✅ 无泄漏数据集创建完成!")
    print("🛡️ 特征集已去除所有标签泄漏，可以安全用于模型训练!")


if __name__ == "__main__":
    create_no_leakage_dataset()