"""
Product-Level Feature Engineering Module
Maintains product-level granularity without aggregation
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_product_level_features(
    products_df: pd.DataFrame,
    schedule_df: pd.DataFrame,
    market_share_df: pd.DataFrame
) -> pd.DataFrame:
    """
    Create product-level features without aggregation
    
    Args:
        products_df (pd.DataFrame): Product sales data (47,190 rows)
        schedule_df (pd.DataFrame): Flight schedule data (815 rows)
        market_share_df (pd.DataFrame): Market share data (819 rows)
        
    Returns:
        pd.DataFrame: Product-level feature matrix
    """
    logger.info("Creating product-level features without aggregation")
    
    # Make a copy to avoid modifying original data
    df = products_df.copy()
    
    # Extract flight number from flight1 column
    df['flight'] = df['flight1'].str[:6]
    
    # 1. RD Features - Keep all 31 RD columns as features
    rd_columns = [col for col in df.columns if col.startswith('RD')]
    logger.info(f"Keeping all {len(rd_columns)} RD columns as features")
    
    # Calculate derived RD features
    df['total_bookings'] = df[rd_columns].sum(axis=1)
    df['max_rd'] = df[rd_columns].max(axis=1)
    df['min_rd'] = df[rd_columns].min(axis=1)
    df['rd_std'] = df[rd_columns].std(axis=1)
    df['rd_range'] = df['max_rd'] - df['min_rd']
    
    # Early vs Late booking patterns
    early_rd_cols = rd_columns[:len(rd_columns)//3]  # First 1/3
    late_rd_cols = rd_columns[-len(rd_columns)//3:]  # Last 1/3
    df['early_bookings'] = df[early_rd_cols].sum(axis=1)
    df['late_bookings'] = df[late_rd_cols].sum(axis=1)
    df['early_late_ratio'] = df['early_bookings'] / (df['late_bookings'] + 1e-8)
    
    # 2. Product Features
    # Keep original categorical features
    df['class_encoded'] = pd.Categorical(df['class']).codes
    df['fare_log'] = np.log1p(df['fare'])  # Log-transform fare to handle skewness
    
    # 3. Flight Features - Merge with schedule data
    schedule_features = schedule_df[[
        'flight', 'origin', 'destination', 'flight_duration',
        'dep_minutes_utc', 'arr_minutes_utc', 'is_multi_leg'
    ]].copy()
    
    # Create time features from UTC minutes
    schedule_features['dep_hour'] = (schedule_features['dep_minutes_utc'] // 60) % 24
    schedule_features['dep_minute'] = schedule_features['dep_minutes_utc'] % 60
    schedule_features['arr_hour'] = (schedule_features['arr_minutes_utc'] // 60) % 24
    
    # Time of day categories
    def get_time_category(hour):
        if 5 <= hour < 12:
            return 'morning'
        elif 12 <= hour < 17:
            return 'afternoon'
        elif 17 <= hour < 21:
            return 'evening'
        else:
            return 'night'
    
    schedule_features['time_of_day'] = schedule_features['dep_hour'].apply(get_time_category)
    schedule_features['is_rush_hour'] = (
        ((7 <= schedule_features['dep_hour']) & (schedule_features['dep_hour'] <= 9)) |
        ((17 <= schedule_features['dep_hour']) & (schedule_features['dep_hour'] <= 19))
    )
    
    # Merge flight features
    df = df.merge(schedule_features, on=['flight', 'origin', 'destination'], how='left')
    
    # 4. Market Features - Merge with market share data
    df = df.merge(market_share_df, on=['origin', 'destination'], how='left')
    
    # 5. Handle missing values
    # Fill missing flight features with defaults
    df['flight_duration'].fillna(df['flight_duration'].median(), inplace=True)
    df['dep_hour'].fillna(12, inplace=True)  # Default to noon
    df['dep_minute'].fillna(0, inplace=True)
    df['arr_hour'].fillna(14, inplace=True)  # Default to 2 PM
    df['time_of_day'].fillna('afternoon', inplace=True)
    df['is_rush_hour'].fillna(False, inplace=True)
    df['is_multi_leg'].fillna(False, inplace=True)
    df['market_share'].fillna(0.5, inplace=True)  # Default market share
    
    # Fill missing RD-derived features
    numeric_cols = ['rd_std', 'rd_range', 'early_late_ratio']
    for col in numeric_cols:
        df[col].fillna(0, inplace=True)
    
    logger.info(f"Created product-level features: {df.shape}")
    logger.info(f"Features with missing values: {df.isnull().sum().sum()}")
    
    return df

def prepare_product_level_dataset(
    products_df: pd.DataFrame,
    schedule_df: pd.DataFrame,
    market_share_df: pd.DataFrame,
    target_column: str = 'total_bookings'
) -> Tuple[pd.DataFrame, pd.Series]:
    """
    Prepare product-level dataset for modeling
    
    Args:
        products_df (pd.DataFrame): Product sales data
        schedule_df (pd.DataFrame): Flight schedule data
        market_share_df (pd.DataFrame): Market share data
        target_column (str): Target variable column name
        
    Returns:
        Tuple[pd.DataFrame, pd.Series]: (features, target)
    """
    logger.info("Preparing product-level dataset")
    
    # Create features
    feature_df = create_product_level_features(products_df, schedule_df, market_share_df)
    
    # Define feature columns (exclude target and ID columns)
    exclude_cols = [
        'flight1', 'Flight2', 'Flight3',  # Original flight columns
        'total_bookings',  # Target variable
        'max_rd', 'min_rd'  # Intermediate calculations
    ]
    
    # Select feature columns
    feature_cols = [col for col in feature_df.columns if col not in exclude_cols]
    
    # Prepare features and target
    X = feature_df[feature_cols].copy()
    y = feature_df[target_column].copy()
    
    # Encode categorical variables
    categorical_cols = ['origin', 'destination', 'flight', 'class', 'time_of_day']
    for col in categorical_cols:
        if col in X.columns:
            X[col] = pd.Categorical(X[col]).codes
    
    # Convert boolean columns to int
    bool_cols = X.select_dtypes(include=['bool']).columns
    X[bool_cols] = X[bool_cols].astype(int)
    
    logger.info(f"Final feature matrix: {X.shape}")
    logger.info(f"Target variable: {len(y)} samples")
    logger.info(f"Feature columns: {len(feature_cols)}")
    
    return X, y

if __name__ == "__main__":
    print("Product-Level Feature Engineering Module")
    print("Maintains product-level granularity for better ML performance")
