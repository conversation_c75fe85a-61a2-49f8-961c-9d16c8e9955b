"""
分舱位需求预测数据准备脚本
基于统一基础数据集，为每个舱位等级准备专门的训练数据
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
import pickle
import json
import os
from pathlib import Path


class CabinClassDemandPreparer:
    """分舱位需求预测数据准备器"""
    
    def __init__(self, data_path: str = "data/"):
        self.data_path = Path(data_path)
        self.unified_dataset = None
        self.cabin_classes = ['B', 'G', 'H', 'K', 'L', 'M', 'Q', 'S', 'T', 'V', 'Y']
        self.datasets = {}
        
    def load_unified_dataset(self):
        """加载统一基础数据集"""
        print("加载统一基础数据集...")
        self.unified_dataset = pd.read_csv(self.data_path / "processed" / "unified_base_dataset.csv")
        print(f"加载数据集: {self.unified_dataset.shape[0]} 行 × {self.unified_dataset.shape[1]} 列")
        
    def prepare_cabin_demand_data(self):
        """为每个舱位等级准备训练数据"""
        print(f"为 {len(self.cabin_classes)} 个舱位等级准备训练数据...")
        
        cabin_datasets = {}
        
        for cabin_class in self.cabin_classes:
            print(f"\n--- 处理舱位 {cabin_class} ---")
            
            # 获取该舱位的目标变量
            target_variable = f"class_{cabin_class}_total"
            
            # 检查目标变量是否存在且有效
            if target_variable not in self.unified_dataset.columns:
                print(f"警告: 舱位 {cabin_class} 的目标变量 {target_variable} 不存在")
                continue
                
            # 统计该舱位的数据情况
            cabin_data_mask = self.unified_dataset[target_variable] > 0
            cabin_flights_count = cabin_data_mask.sum()
            
            print(f"舱位 {cabin_class}:")
            print(f"  - 总预订量均值: {self.unified_dataset[target_variable].mean():.2f}")
            print(f"  - 有需求的航班数: {cabin_flights_count}")
            print(f"  - 需求航班比例: {cabin_flights_count / len(self.unified_dataset) * 100:.1f}%")
            
            # 创建舱位专用数据集
            cabin_df = self.unified_dataset.copy()
            
            # 设置目标变量
            y = cabin_df[target_variable]
            
            # 数据预处理
            X_processed, feature_names, encoders, scaler = self._preprocess_features(cabin_df, cabin_class)
            
            # 数据分割
            X_train, X_test, y_train, y_test = train_test_split(
                X_processed, y, test_size=0.2, random_state=42, stratify=self._create_strata(y)
            )
            
            # 保存数据集信息
            dataset_info = {
                "cabin_class": cabin_class,
                "target_variable": target_variable,
                "n_features": len(feature_names),
                "train_samples": len(X_train),
                "test_samples": len(X_test),
                "features": feature_names,
                "mean_target": float(y_train.mean()),
                "std_target": float(y_train.std()),
                "min_target": float(y_train.min()),
                "max_target": float(y_train.max()),
                "cabin_flights_count": int(cabin_flights_count),
                "cabin_demand_ratio": float(cabin_flights_count / len(self.unified_dataset))
            }
            
            # 如果所有值都是0，添加标记
            if y_train.sum() == 0 and y_test.sum() == 0:
                dataset_info["all_zero_demand"] = True
                print(f"警告: 舱位 {cabin_class} 所有航班需求为0")
            else:
                dataset_info["all_zero_demand"] = False
            
            cabin_datasets[cabin_class] = {
                'X_train': X_train,
                'X_test': X_test,
                'y_train': y_train,
                'y_test': y_test,
                'info': dataset_info,
                'encoders': encoders,
                'scaler': scaler
            }
            
            print(f"  - 训练集: {len(X_train)} 样本")
            print(f"  - 测试集: {len(X_test)} 样本")
            
        self.datasets = cabin_datasets
        
    def _preprocess_features(self, df: pd.DataFrame, cabin_class: str):
        """特征预处理"""
        print(f"为舱位 {cabin_class} 执行特征预处理...")
        
        # 特征选择 - 排除其他舱位的目标变量
        features_to_exclude = []
        for other_cabin in self.cabin_classes:
            exclude_features = [
                f"class_{other_cabin}_total",
                f"class_{other_cabin}_peak", 
                f"class_{other_cabin}_std",
                f"class_{other_cabin}_early_ratio",
                f"class_{other_cabin}_medium_ratio",
                f"class_{other_cabin}_long_ratio"
            ]
            features_to_exclude.extend(exclude_features)
        
        # 排除聚合目标变量
        features_to_exclude.extend(['total_rd_bookings', 'rd_bookings_std'])
        
        # 排除标识列
        features_to_exclude.extend(['flight_id', 'origin', 'destination', 'route'])
        
        # 构建特征列表
        all_features = df.columns.tolist()
        core_features = [f for f in all_features if f not in features_to_exclude]
        
        print(f"选中 {len(core_features)} 个核心特征 (排除 {len(features_to_exclude)} 个特征)")
        
        X = df[core_features].copy()
        
        # 识别数值型和分类型变量
        numeric_features = X.select_dtypes(include=[np.number]).columns.tolist()
        categorical_features = X.select_dtypes(include=['object', 'bool']).columns.tolist()
        
        print(f"  - 数值特征: {len(numeric_features)} 个")
        print(f"  - 分类特征: {len(categorical_features)} 个")
        
        # 处理分类变量
        encoders = {}
        for col in categorical_features:
            le = LabelEncoder()
            X[f'{col}_encoded'] = le.fit_transform(X[col].astype(str))
            encoders[col] = le
            X = X.drop(col, axis=1)
        
        # 特征工程 - 添加舱位特定特征
        X_engineered = self._engineer_cabin_features(X, df, cabin_class)
        
        # 标准化
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X_engineered)
        
        feature_names = X_engineered.columns.tolist()
        
        return X_scaled, feature_names, encoders, scaler
    
    def _engineer_cabin_features(self, X: pd.DataFrame, original_df: pd.DataFrame, cabin_class: str):
        """为特定舱位添加工程特征"""
        print(f"为舱位 {cabin_class} 添加工程特征...")
        
        X_engineered = X.copy()
        
        # 1. 舱位强度特征
        X_engineered['cabin_demand_strength'] = (
            X_engineered.get(f'class_{cabin_class}_early_ratio', 0) +
            X_engineered.get(f'class_{cabin_class}_medium_ratio', 0) +
            X_engineered.get(f'class_{cabin_class}_long_ratio', 0)
        )
        
        # 2. 舱位波动性特征
        X_engineered['cabin_volatility'] = X_engineered.get(f'class_{cabin_class}_std', 0) / (
            X_engineered.get(f'class_{cabin_class}_total', 0) + 1
        )
        
        # 3. 舱位预订集中度
        X_engineered['cabin_concentration'] = X_engineered.get(f'class_{cabin_class}_peak', 0) / (
            X_engineered.get(f'class_{cabin_class}_total', 0) + 1
        )
        
        # 4. 舱位活跃度标记
        cabin_total_value = X_engineered.get(f'class_{cabin_class}_total', 0)
        X_engineered['cabin_active'] = (1 if cabin_total_value > 0 else 0)
        
        # 5. 航线级别舱位特征
        route_cabin_stats = original_df.groupby('route')[f'class_{cabin_class}_total'].agg(['mean', 'std', 'max']).reset_index()
        route_cabin_stats.columns = ['route', f'route_{cabin_class}_avg', f'route_{cabin_class}_std', f'route_{cabin_class}_max']
        
        # 创建航线映射字典
        route_mapping = {}
        for i, route in enumerate(original_df['route']):
            route_mapping[i] = route
            
        # 重新建立X_engineered的索引，确保与original_df对齐
        X_engineered.index = original_df.index
        
        # 添加航线特征
        for col in [f'route_{cabin_class}_avg', f'route_{cabin_class}_std', f'route_{cabin_class}_max']:
            X_engineered[col] = original_df['route'].map(route_cabin_stats.set_index('route')[col]).fillna(0)
        
        # 6. 时段舱位特征
        if 'departure_period' in original_df.columns:
            period_cabin_stats = original_df.groupby('departure_period')[f'class_{cabin_class}_total'].mean().to_dict()
            X_engineered['period_cabin_avg'] = original_df['departure_period'].map(period_cabin_stats).fillna(0)
        
        # 7. 竞争环境舱位特征
        if 'competition_level' in original_df.columns:
            competition_cabin_stats = original_df.groupby('competition_level')[f'class_{cabin_class}_total'].mean().to_dict()
            X_engineered['competition_cabin_avg'] = original_df['competition_level'].map(competition_cabin_stats).fillna(0)
        
        # 8. 舱位相对重要性
        total_cabin_cols = [col for col in original_df.columns if col.startswith('class_') and col.endswith('_total')]
        cabin_total = original_df[f'class_{cabin_class}_total']
        total_all_cabins = original_df[total_cabin_cols].sum(axis=1)
        X_engineered['cabin_share_ratio'] = cabin_total / (total_all_cabins + 1)
        
        print(f"  - 添加工程特征后特征数: {X_engineered.shape[1]} 个")
        
        return X_engineered
    
    def _create_strata(self, y: pd.Series):
        """创建分层抽样的分层依据"""
        # 对于稀疏数据，将目标变量分为几个区间
        if y.sum() == 0:
            return np.zeros(len(y))  # 全部为0，无需分层
        
        # 创建分层：无需求、低需求、中需求、高需求
        q75 = y.quantile(0.75)
        q50 = y.quantile(0.5)
        q25 = y.quantile(0.25)
        
        strata = np.zeros(len(y))
        strata[y == 0] = 0
        strata[(y > 0) & (y <= q25)] = 1
        strata[(y > q25) & (y <= q50)] = 2
        strata[(y > q50) & (y <= q75)] = 3
        strata[y > q75] = 4
        
        return strata
    
    def save_datasets(self):
        """保存所有舱位的数据集"""
        print("保存分舱位数据集...")
        
        output_path = self.data_path / "processed" / "cabin_demand"
        output_path.mkdir(exist_ok=True)
        
        cabin_summary = {}
        
        for cabin_class, dataset in self.datasets.items():
            print(f"保存舱位 {cabin_class} 数据...")
            
            cabin_path = output_path / cabin_class
            cabin_path.mkdir(exist_ok=True)
            
            # 保存数据文件
            pd.DataFrame(dataset['X_train']).to_csv(cabin_path / f"{cabin_class}_X_train.csv", index=False)
            pd.DataFrame(dataset['X_test']).to_csv(cabin_path / f"{cabin_class}_X_test.csv", index=False)
            pd.DataFrame(dataset['y_train']).to_csv(cabin_path / f"{cabin_class}_y_train.csv", index=False)
            pd.DataFrame(dataset['y_test']).to_csv(cabin_path / f"{cabin_class}_y_test.csv", index=False)
            
            # 保存特征列表
            pd.DataFrame({
                'feature_name': dataset['info']['features'],
                'feature_type': ['unknown'] * len(dataset['info']['features'])  # 可以后续完善类型信息
            }).to_csv(cabin_path / f"{cabin_class}_features.csv", index=False)
            
            # 保存元数据
            with open(cabin_path / f"{cabin_class}_dataset_info.json", 'w', encoding='utf-8') as f:
                json.dump(dataset['info'], f, ensure_ascii=False, indent=2)
            
            # 保存预处理器
            with open(cabin_path / f"{cabin_class}_scaler.pkl", 'wb') as f:
                pickle.dump(dataset['scaler'], f)
            
            with open(cabin_path / f"{cabin_class}_label_encoders.pkl", 'wb') as f:
                pickle.dump(dataset['encoders'], f)
            
            cabin_summary[cabin_class] = dataset['info']
        
        # 保存总体汇总信息
        with open(output_path / "cabin_demand_summary.json", 'w', encoding='utf-8') as f:
            json.dump(cabin_summary, f, ensure_ascii=False, indent=2)
        
        print(f"所有舱位数据集已保存到: {output_path}")
        
    def generate_summary_report(self):
        """生成分舱位数据准备总结报告"""
        print("\n" + "="*50)
        print("分舱位需求预测数据准备总结报告")
        print("="*50)
        
        report_lines = []
        report_lines.append("# 分舱位需求预测数据准备报告\n")
        report_lines.append(f"## 概述\n")
        report_lines.append(f"共处理 {len(self.cabin_classes)} 个舱位等级: {', '.join(self.cabin_classes)}\n")
        
        total_samples = 0
        active_cabins = 0
        
        for cabin_class, dataset in self.datasets.items():
            info = dataset['info']
            total_samples += info['train_samples'] + info['test_samples']
            
            if info['cabin_flights_count'] > 0:
                active_cabins += 1
            
            report_lines.append(f"### 舱位 {cabin_class}\n")
            report_lines.append(f"- **数据规模**: {info['train_samples']} 训练 + {info['test_samples']} 测试")
            report_lines.append(f"- **特征数量**: {info['n_features']}")
            report_lines.append(f"- **目标变量**: {info['target_variable']}")
            report_lines.append(f"- **需求均值**: {info['mean_target']:.2f}")
            report_lines.append(f"- **需求范围**: {info['min_target']:.2f} - {info['max_target']:.2f}")
            report_lines.append(f"- **有需求航班**: {info['cabin_flights_count']} ({info['cabin_demand_ratio']*100:.1f}%)")
            
            if info.get('all_zero_demand', False):
                report_lines.append(f"- **注意**: 所有航班需求为0，可能不适合预测")
            
            report_lines.append("")
        
        report_lines.append(f"## 总体统计\n")
        report_lines.append(f"- **总舱位数**: {len(self.cabin_classes)}")
        report_lines.append(f"- **活跃舱位数**: {active_cabins} (有数据)")
        report_lines.append(f"- **总样本数**: {total_samples}")
        report_lines.append(f"- **平均每舱位特征数**: {np.mean([d['info']['n_features'] for d in self.datasets.values()]):.0f}\n")
        
        report_lines.append(f"## 舱位需求分析\n")
        
        # 分析各舱位的需求分布
        cabin_demands = []
        for cabin_class, dataset in self.datasets.items():
            info = dataset['info']
            cabin_demands.append({
                'cabin': cabin_class,
                'mean_demand': info['mean_target'],
                'flights_with_demand': info['cabin_flights_count'],
                'demand_ratio': info['cabin_demand_ratio']
            })
        
        # 按需求均值排序
        cabin_demands.sort(key=lambda x: x['mean_demand'], reverse=True)
        
        for cabin in cabin_demands:
            report_lines.append(f"- **{cabin['cabin']}舱位**: 均值 {cabin['mean_demand']:.1f}, 活跃航班 {cabin['flights_with_demand']} ({cabin['demand_ratio']*100:.1f}%)")
        
        report_lines.append("\n## 数据质量评估\n")
        
        # 数据质量评估
        quality_issues = []
        for cabin_class, dataset in self.datasets.items():
            info = dataset['info']
            
            if info['all_zero_demand']:
                quality_issues.append(f"舱位 {cabin_class}: 全零数据，可能不适合训练")
            
            if info['cabin_flights_count'] < 10:
                quality_issues.append(f"舱位 {cabin_class}: 活跃航班数过少 ({info['cabin_flights_count']})")
            
            if info['mean_target'] < 0.1:
                quality_issues.append(f"舱位 {cabin_class}: 需求均值过低 ({info['mean_target']:.3f})")
        
        if quality_issues:
            report_lines.append("### 发现的质量问题:\n")
            for issue in quality_issues:
                report_lines.append(f"- {issue}")
        else:
            report_lines.append("### 数据质量良好，未发现明显问题\n")
        
        report_lines.append("## 使用建议\n")
        report_lines.append("1. **高需求舱位**: {Y, M, K} 等模式识别和预测效果好".format(
            **{"Y, M, K": ", ".join([c['cabin'] for c in cabin_demands[:3] if c['mean_demand'] > 1])}
        ))
        report_lines.append("2. **中等需求舱位**: {Q, L, G} 需要考虑稀疏数据处理".format(
            **{"Q, L, G": ", ".join([c['cabin'] for c in cabin_demands[3:6]])}
        ))
        report_lines.append("3. **低需求舱位**: {B, S, T, V, H} 建议使用专门的小样本学习方法".format(
            **{"B, S, T, V, H": ", ".join([c['cabin'] for c in cabin_demands[6:] if c['mean_demand'] <= 0.1])}
        ))
        
        report_lines.append("\n## 文件结构\n")
        report_lines.append("```\ndata/processed/cabin_demand/\n")
        for cabin_class in sorted(self.datasets.keys()):
            report_lines.append(f"├── {cabin_class}/\n")
            report_lines.append(f"│   ├── {cabin_class}_X_train.csv     # 训练特征\n")
            report_lines.append(f"│   ├── {cabin_class}_X_test.csv      # 测试特征\n")
            report_lines.append(f"│   ├── {cabin_class}_y_train.csv     # 训练目标\n")
            report_lines.append(f"│   ├── {cabin_class}_y_test.csv      # 测试目标\n")
            report_lines.append(f"│   ├── {cabin_class}_features.csv    # 特征列表\n")
            report_lines.append(f"│   ├── {cabin_class}_dataset_info.json # 数据集信息\n")
            report_lines.append(f"│   ├── {cabin_class}_scaler.pkl       # 标准化器\n")
            report_lines.append(f"│   └── {cabin_class}_label_encoders.pkl # 编码器\n")
        report_lines.append("└── cabin_demand_summary.json           # 总体汇总\n")
        report_lines.append("```\n")
        
        report_lines.append("---\n")
        report_lines.append(f"*生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}*")
        report_lines.append(f"*作者: Claude for IE2025项目*")
        
        # 保存报告
        report_content = "\n".join(report_lines)
        with open("results/cabin_demand_data_preparation_report.md", "w", encoding="utf-8") as f:
            f.write(report_content)
        
        # 打印报告
        print("\n".join(report_lines[:50]))  # 打印报告开头部分
        
        return report_content


def main():
    """主函数"""
    print("开始分舱位需求预测数据准备...")
    
    # 创建数据准备器
    preparer = CabinClassDemandPreparer()
    
    # 加载统一数据集
    preparer.load_unified_dataset()
    
    # 准备分舱位数据
    preparer.prepare_cabin_demand_data()
    
    # 保存数据集
    preparer.save_datasets()
    
    # 生成总结报告
    report = preparer.generate_summary_report()
    
    print("\n分舱位需求预测数据准备完成!")
    return preparer


if __name__ == "__main__":
    preparer = main()