"""
构建统一基础数据集
整合航班时刻表、机队信息、市场份额和产品销售数据
为7个核心预测目标提供统一的数据基础

作者: Claude for IE2025项目
日期: 2025-07-31
"""

import pandas as pd
import numpy as np
import re
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional

class UnifiedDatasetBuilder:
    def __init__(self, data_path: str = "data/"):
        self.data_path = data_path
        self.schedule_data = None
        self.fleet_data = None  
        self.market_share_data = None
        self.products_data = None
        self.unified_dataset = None
        
    def load_raw_data(self):
        """加载原始数据文件"""
        print("🚀 开始加载原始数据文件...")
        
        # 加载航班时刻表
        self.schedule_data = pd.read_csv(f"{self.data_path}data_fam_schedule.csv")
        print(f"✅ 航班时刻表: {len(self.schedule_data)} 条记录")
        
        # 加载机队信息
        self.fleet_data = pd.read_csv(f"{self.data_path}data_fam_fleet.csv")
        print(f"✅ 机队信息: {len(self.fleet_data)} 种机型")
        
        # 加载市场份额
        self.market_share_data = pd.read_csv(f"{self.data_path}data_fam_market_share.csv")
        print(f"✅ 市场份额: {len(self.market_share_data)} 条航线记录")
        
        # 加载产品销售历史
        self.products_data = pd.read_csv(f"{self.data_path}data_fam_products.csv")
        print(f"✅ 产品销售: {len(self.products_data)} 条产品记录")
        
    def parse_aircraft_config(self, config: str) -> Tuple[int, int, int]:
        """解析机型配置编码: F8C12Y126 -> (8, 12, 126)"""
        pattern = r'F(\d+)C(\d+)Y(\d+)'
        match = re.match(pattern, config)
        if match:
            return int(match.group(1)), int(match.group(2)), int(match.group(3))
        return 0, 0, 0
    
    def calculate_utc_time(self, local_time: int, offset: int) -> float:
        """计算UTC时间"""
        # 将时间转换为小时
        local_hour = local_time // 100 + (local_time % 100) / 60
        offset_hour = offset / 60
        return local_hour - offset_hour
    
    def time_str_to_minutes(self, time_str: int) -> int:
        """将时间字符串转换为分钟数"""
        hours = time_str // 100
        minutes = time_str % 100
        return hours * 60 + minutes
    
    def calculate_flight_duration(self, row) -> float:
        """计算航班飞行时长（小时）"""
        # 计算本地时间
        dep_minutes = self.time_str_to_minutes(row['deptime'])
        arr_minutes = self.time_str_to_minutes(row['arrtime'])
        
        # 考虑时区偏移
        dep_utc = dep_minutes - row['depoff']
        arr_utc = arr_minutes - row['arroff']
        
        # 计算时长
        duration = (arr_utc - dep_utc) / 60.0
        
        # 处理跨天情况
        if duration < 0:
            duration += 24
            
        return duration
    
    def is_weekend_flight(self, departure_time: int) -> bool:
        """判断是否为周末航班（简化处理）"""
        # 实际应用中需要基于具体日期，这里使用简化逻辑
        # 可以根据航班时间特征来标记周末倾向
        evening_flights = departure_time >= 1800 or departure_time <= 600
        return evening_flights
    
    def classify_time_period(self, hour: int) -> str:
        """分类航班时间段"""
        if 6 <= hour < 12:
            return "morning"
        elif 12 <= hour < 18:
            return "afternoon"
        elif 18 <= hour < 24:
            return "evening"
        else:
            return "red_eye"
    
    def classify_market_competition(self, share: float) -> str:
        """分类市场竞争环境"""
        if share >= 0.8:
            return "monopoly"
        elif share >= 0.5:
            return "dominant"
        elif share >= 0.2:
            return "competitive"
        else:
            return "weak"
    
    def identify_hub_airports(self) -> List[str]:
        """识别枢纽机场"""
        # 统计各机场的航班频次
        departure_counts = self.schedule_data['origin'].value_counts()
        arrival_counts = self.schedule_data['destination'].value_counts()
        
        total_counts = departure_counts.add(arrival_counts, fill_value=0)
        
        # 选择航班频次排名前10的机场作为枢纽
        hub_airports = total_counts.nlargest(10).index.tolist()
        
        print(f"🏆 识别到枢纽机场: {hub_airports}")
        return hub_airports
    
    def extract_route_features(self, row, hub_airports: List[str]) -> Dict:
        """提取航线特征"""
        origin = row['origin']
        destination = row['destination']
        route = f"{origin}-{destination}"
        
        # 获取市场份额
        market_share_row = self.market_share_data[
            (self.market_share_data['Org'] == origin) & 
            (self.market_share_data['Des'] == destination)
        ]
        
        market_share = market_share_row['Host_share'].iloc[0] if len(market_share_row) > 0 else 0.5
        
        return {
            'route': route,
            'market_share': market_share,
            'competition_level': self.classify_market_competition(market_share),
            'is_hub_origin': 1 if origin in hub_airports else 0,
            'is_hub_destination': 1 if destination in hub_airports else 0,
            'is_hub_to_hub': 1 if (origin in hub_airports and destination in hub_airports) else 0,
            'same_airport': 1 if origin == destination else 0
        }
    
    def extract_fleet_features(self) -> Dict:
        """提取机队特征"""
        fleet_features = {}
        
        for _, row in self.fleet_data.iterrows():
            aircraft_type = row['机型']
            total_seats = row['座位数']
            aircraft_count = row['飞机数量']
            hourly_cost = row['每小时飞行成本']
            
            # 解析舱位配置
            first_class, business_class, economy_class = self.parse_aircraft_config(aircraft_type)
            
            fleet_features[aircraft_type] = {
                'total_seats': total_seats,
                'aircraft_count': aircraft_count,
                'hourly_cost': hourly_cost,
                'first_class_seats': first_class,
                'business_class_seats': business_class,
                'economy_class_seats': economy_class,
                'cost_per_seat': hourly_cost / total_seats if total_seats > 0 else 0,
                'has_first_class': 1 if first_class > 0 else 0,
                'has_business_class': 1 if business_class > 0 else 0
            }
        
        return fleet_features
    
    def identify_multi_stop_flights(self) -> Dict:
        """识别经停航班"""
        multi_stop_flights = {}
        
        # 统计Flight1字段模式
        flight_products = self.products_data['Flight1'].value_counts()
        
        # 识别多航段航班（Flight1字段包含多个航班号）
        for flight_code in flight_products.index:
            if flight_code != '.' and 'BERBOD' not in flight_code:  # 排除简单航线
                # 检查是否为多航段模式
                segments = self.products_data[
                    self.products_data['Flight1'] == flight_code
                ]
                
                if len(segments) > 1:
                    multi_stop_flights[flight_code] = {
                        'type': 'multi_stop',
                        'segments_count': len(segments),
                        'segments': segments[['Origin', 'Destination', 'Flight2', 'Flight3']].to_dict('records')
                    }
        
        print(f"🔄 识别到经停航班: {len(multi_stop_flights)} 个")
        return multi_stop_flights
    
    def extract_rd_aggregate_features(self, product_rows) -> Dict:
        """提取RD时序的聚合特征"""
        if product_rows.empty:
            return {}
        
        # RD字段列表
        rd_columns = [col for col in product_rows.columns if col.startswith('RD')]
        
        aggregate_features = {}
        
        for cabin_class in product_rows['Class'].unique():
            cabin_data = product_rows[product_rows['Class'] == cabin_class]
            
            if cabin_data.empty:
                continue
                
            # 获取该舱位的RD数据
            rd_data = cabin_data[rd_columns].iloc[0] if len(cabin_data) > 0 else pd.Series(0, index=rd_columns)
            
            # 计算聚合特征
            total_bookings = rd_data.sum()
            peak_booking = rd_data.max()
            booking_std = rd_data.std()
            
            # 预订模式特征
            early_bookings = rd_data[['RD0', 'RD1', 'RD2', 'RD4', 'RD6']].sum()
            medium_bookings = rd_data[['RD7', 'RD8', 'RD11', 'RD14', 'RD16', 'RD18', 'RD21', 'RD24', 'RD28']].sum()
            long_term_bookings = rd_data[['RD33', 'RD39', 'RD44', 'RD49', 'RD59', 'RD69', 'RD79', 'RD89', 'RD99']].sum()
            
            aggregate_features[f'class_{cabin_class}_total'] = total_bookings
            aggregate_features[f'class_{cabin_class}_peak'] = peak_booking
            aggregate_features[f'class_{cabin_class}_std'] = booking_std
            aggregate_features[f'class_{cabin_class}_early_ratio'] = early_bookings / total_bookings if total_bookings > 0 else 0
            aggregate_features[f'class_{cabin_class}_medium_ratio'] = medium_bookings / total_bookings if total_bookings > 0 else 0
            aggregate_features[f'class_{cabin_class}_long_ratio'] = long_term_bookings / total_bookings if total_bookings > 0 else 0
        
        # 总体特征
        all_rd_data = product_rows[rd_columns].values.sum(axis=0) if len(product_rows) > 0 else np.zeros(len(rd_columns))
        aggregate_features['total_rd_bookings'] = all_rd_data.sum()
        aggregate_features['rd_bookings_std'] = all_rd_data.std()
        aggregate_features['has_historical_data'] = 1 if len(product_rows) > 0 else 0
        
        return aggregate_features
    
    def build_unified_dataset(self):
        """构建统一基础数据集"""
        print("🏗️ 开始构建统一基础数据集...")
        
        # 1. 基础特征工程
        print("📊 步骤1: 基础特征工程...")
        
        # 识别枢纽机场
        hub_airports = self.identify_hub_airports()
        
        # 提取机队特征
        fleet_features = self.extract_fleet_features()
        
        # 识别经停航班
        multi_stop_flights = self.identify_multi_stop_flights()
        
        # 2. 为每个航班构建特征
        print("✈️ 步骤2: 为每个航班构建特征...")
        
        unified_features = []
        
        for _, flight_row in self.schedule_data.iterrows():
            flight_id = flight_row['flight']
            origin = flight_row['origin']
            destination = flight_row['destination']
            
            # 基础航班信息
            features = {
                'flight_id': flight_id,
                'origin': origin,
                'destination': destination
            }
            
            # 时间特征
            dep_hour = flight_row['deptime'] // 100
            arr_hour = flight_row['arrtime'] // 100
            
            features.update({
                'departure_time': flight_row['deptime'],
                'arrival_time': flight_row['arrtime'],
                'departure_hour': dep_hour,
                'arrival_hour': arr_hour,
                'departure_period': self.classify_time_period(dep_hour),
                'arrival_period': self.classify_time_period(arr_hour),
                'weekend_flight': self.is_weekend_flight(flight_row['deptime']),
                'flight_duration': self.calculate_flight_duration(flight_row),
                'dep_offset': flight_row['depoff'],
                'arr_offset': flight_row['arroff']
            })
            
            # 航线特征
            route_features = self.extract_route_features(flight_row, hub_airports)
            features.update(route_features)
            
            # 航班类型特征
            is_multi_stop = flight_id in multi_stop_flights
            features.update({
                'is_multi_stop': 1 if is_multi_stop else 0,
                'flight_type': 'multi_stop' if is_multi_stop else 'direct'
            })
            
            # 获取产品数据（历史需求数据）
            # 构造匹配条件，尝试多种可能的Flight1格式
            possible_patterns = [
                flight_id,
                f"{origin}{destination}",  # 如 BERBOD
                f"{flight_id}{origin}{destination}",  # 如 AA0001BERBOD
            ]
            
            matched_products = pd.DataFrame()
            for pattern in possible_patterns:
                matched = self.products_data[self.products_data['Flight1'] == pattern]
                if not matched.empty:
                    matched_products = matched
                    break
            
            # RD历史特征
            rd_features = self.extract_rd_aggregate_features(matched_products)
            features.update(rd_features)
            
            # 通用机型特征（所有可能机型的统计信息）
            avg_seats = self.fleet_data['座位数'].mean()
            max_seats = self.fleet_data['座位数'].max()
            min_seats = self.fleet_data['座位数'].min()
            
            features.update({
                'avg_fleet_seats': avg_seats,
                'max_fleet_seats': max_seats,
                'min_fleet_seats': min_seats,
                'fleet_aircraft_types': len(self.fleet_data)
            })
            
            unified_features.append(features)
        
        # 3. 创建数据集
        print("📋 步骤3: 创建统一数据集...")
        
        self.unified_dataset = pd.DataFrame(unified_features)
        
        # 4. 数据清洗和后处理
        print("🧹 步骤4: 数据清洗和后处理...")
        
        # 处理缺失值
        numeric_columns = self.unified_dataset.select_dtypes(include=[np.number]).columns
        self.unified_dataset[numeric_columns] = self.unified_dataset[numeric_columns].fillna(0)
        
        # 处理分类变量
        categorical_columns = ['departure_period', 'arrival_period', 'competition_level', 'flight_type']
        for col in categorical_columns:
            if col in self.unified_dataset.columns:
                self.unified_dataset[col] = self.unified_dataset[col].fillna('unknown')
        
        print(f"✅ 统一基础数据集构建完成: {len(self.unified_dataset)} 行 x {len(self.unified_dataset.columns)} 列")
        
        return self.unified_dataset
    
    def save_dataset(self, output_path: str = "data/processed/unified_base_dataset.csv"):
        """保存统一数据集"""
        if self.unified_dataset is not None:
            # 确保输出目录存在
            import os
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            self.unified_dataset.to_csv(output_path, index=False)
            print(f"💾 统一数据集已保存到: {output_path}")
        else:
            print("❌ 数据集未构建，请先调用 build_unified_dataset()")
    
    def get_dataset_info(self):
        """获取数据集基本信息"""
        if self.unified_dataset is not None:
            info = {
                '总行数': len(self.unified_dataset),
                '总列数': len(self.unified_dataset.columns),
                '缺失值统计': self.unified_dataset.isnull().sum().sum(),
                '有历史数据的航班数': (self.unified_dataset['has_historical_data'] == 1).sum(),
                '无历史数据的航班数': (self.unified_dataset['has_historical_data'] == 0).sum(),
                '经停航班数': (self.unified_dataset['is_multi_stop'] == 1).sum(),
                '直飞航班数': (self.unified_dataset['is_multi_stop'] == 0).sum()
            }
            
            print("📊 数据集基本信息:")
            for key, value in info.items():
                print(f"  {key}: {value}")
            
            return info
        else:
            print("❌ 数据集未构建")
            return None
    
    def get_feature_categories(self):
        """获取特征分类信息"""
        if self.unified_dataset is not None:
            numeric_features = self.unified_dataset.select_dtypes(include=[np.number]).columns.tolist()
            categorical_features = self.unified_dataset.select_dtypes(include=['object']).columns.tolist()
            
            feature_info = {
                '数值型特征': len(numeric_features),
                '分类型特征': len(categorical_features),
                '总特征数': len(self.unified_dataset.columns)
            }
            
            print("📈 特征分类信息:")
            for key, value in feature_info.items():
                print(f"  {key}: {value}")
            
            print(f"\n🔢 数值型特征示例: {numeric_features[:5]}...")
            print(f"🔤 分类型特征示例: {categorical_features[:5]}...")
            
            return {'numeric': numeric_features, 'categorical': categorical_features}
        else:
            print("❌ 数据集未构建")
            return None

def main():
    """主函数：构建统一基础数据集"""
    print("🎯 开始构建统一基础数据集...")
    
    # 初始化构建器
    builder = UnifiedDatasetBuilder()
    
    # 加载数据
    builder.load_raw_data()
    
    # 构建数据集
    unified_df = builder.build_unified_dataset()
    
    # 获取数据集信息
    builder.get_dataset_info()
    print()
    
    # 获取特征分类
    builder.get_feature_categories()
    print()
    
    # 保存数据集
    builder.save_dataset()
    
    print("🎉 统一基础数据集构建完成!")
    
    return builder, unified_df

if __name__ == "__main__":
    builder, dataset = main()