"""
No-Leakage Feature Engineering Module
Creates features without data leakage for demand prediction
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_no_leakage_features(
    products_df: pd.DataFrame,
    schedule_df: pd.DataFrame,
    market_share_df: pd.DataFrame
) -> pd.DataFrame:
    """
    Create features without using RD columns to avoid data leakage
    
    Args:
        products_df (pd.DataFrame): Product sales data
        schedule_df (pd.DataFrame): Flight schedule data
        market_share_df (pd.DataFrame): Market share data
        
    Returns:
        pd.DataFrame: Feature matrix without data leakage
    """
    logger.info("Creating features without data leakage (no RD columns)")
    
    # Make a copy and extract flight number
    df = products_df.copy()
    df['flight'] = df['flight1'].str[:6]
    
    # 1. Product-level features (no RD columns)
    logger.info("Adding product-level features")
    
    # Categorical features
    df['class_encoded'] = pd.Categorical(df['class']).codes
    
    # Fare features
    df['fare_log'] = np.log1p(df['fare'])
    df['fare_per_km'] = df['fare'] / (df.merge(
        schedule_df[['flight', 'origin', 'destination', 'flight_duration']], 
        on=['flight', 'origin', 'destination'], 
        how='left'
    )['flight_duration'] + 1)  # Avoid division by zero
    
    # 2. Flight-level features
    logger.info("Adding flight-level features")
    
    # Merge with schedule data
    schedule_features = schedule_df[[
        'flight', 'origin', 'destination', 'flight_duration',
        'dep_minutes_utc', 'arr_minutes_utc', 'is_multi_leg'
    ]].copy()
    
    # Time features
    schedule_features['dep_hour'] = (schedule_features['dep_minutes_utc'] // 60) % 24
    schedule_features['dep_minute'] = schedule_features['dep_minutes_utc'] % 60
    schedule_features['arr_hour'] = (schedule_features['arr_minutes_utc'] // 60) % 24
    
    # Time categories
    def get_time_category(hour):
        if 5 <= hour < 12:
            return 'morning'
        elif 12 <= hour < 17:
            return 'afternoon'
        elif 17 <= hour < 21:
            return 'evening'
        else:
            return 'night'
    
    schedule_features['time_of_day'] = schedule_features['dep_hour'].apply(get_time_category)
    schedule_features['is_rush_hour'] = (
        ((7 <= schedule_features['dep_hour']) & (schedule_features['dep_hour'] <= 9)) |
        ((17 <= schedule_features['dep_hour']) & (schedule_features['dep_hour'] <= 19))
    )
    
    # Flight duration categories
    schedule_features['duration_category'] = pd.cut(
        schedule_features['flight_duration'], 
        bins=[0, 60, 180, 300, np.inf],
        labels=['short', 'medium', 'long', 'very_long']
    )
    
    # Merge flight features
    df = df.merge(schedule_features, on=['flight', 'origin', 'destination'], how='left')
    
    # 3. Market features
    logger.info("Adding market features")
    df = df.merge(market_share_df, on=['origin', 'destination'], how='left')
    
    # 4. Route-level aggregated features (without RD data)
    logger.info("Adding route-level features")
    
    # Calculate route statistics from other products (excluding current product)
    route_stats = df.groupby(['origin', 'destination']).agg({
        'fare': ['mean', 'std', 'min', 'max'],
        'class_encoded': 'nunique'
    }).round(2)
    
    # Flatten column names
    route_stats.columns = ['_'.join(col).strip() for col in route_stats.columns]
    route_stats = route_stats.add_prefix('route_')
    route_stats = route_stats.reset_index()
    
    # Merge route statistics
    df = df.merge(route_stats, on=['origin', 'destination'], how='left')
    
    # 5. Flight-level aggregated features
    logger.info("Adding flight-level features")
    
    flight_stats = df.groupby(['flight']).agg({
        'fare': ['mean', 'std', 'count'],
        'class_encoded': 'nunique'
    }).round(2)
    
    flight_stats.columns = ['_'.join(col).strip() for col in flight_stats.columns]
    flight_stats = flight_stats.add_prefix('flight_')
    flight_stats = flight_stats.reset_index()
    
    df = df.merge(flight_stats, on=['flight'], how='left')
    
    # 6. Competitive features
    logger.info("Adding competitive features")
    
    # Price position within route
    df['fare_rank_in_route'] = df.groupby(['origin', 'destination'])['fare'].rank(pct=True)
    
    # Price position within flight
    df['fare_rank_in_flight'] = df.groupby(['flight'])['fare'].rank(pct=True)
    
    # Class position (higher class = lower code)
    df['class_premium_level'] = 1 / (df['class_encoded'] + 1)
    
    # 7. Handle missing values
    logger.info("Handling missing values")
    
    # Fill missing flight features
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    df[numeric_cols] = df[numeric_cols].fillna(df[numeric_cols].median())
    
    # Fill missing categorical features
    categorical_cols = df.select_dtypes(include=['object', 'category']).columns
    for col in categorical_cols:
        df[col] = df[col].fillna(df[col].mode().iloc[0] if len(df[col].mode()) > 0 else 'unknown')
    
    logger.info(f"Created no-leakage features: {df.shape}")
    return df

def prepare_no_leakage_dataset(
    products_df: pd.DataFrame,
    schedule_df: pd.DataFrame,
    market_share_df: pd.DataFrame
) -> Tuple[pd.DataFrame, pd.Series]:
    """
    Prepare dataset without data leakage
    
    Returns:
        Tuple[pd.DataFrame, pd.Series]: (features, target)
    """
    logger.info("Preparing no-leakage dataset")
    
    # Create target variable from RD columns
    rd_columns = [col for col in products_df.columns if col.startswith('RD')]
    target = products_df[rd_columns].sum(axis=1)
    
    # Create features without RD columns
    feature_df = create_no_leakage_features(products_df, schedule_df, market_share_df)
    
    # Select feature columns (exclude RD columns and target-related columns)
    exclude_cols = [
        'flight1', 'Flight2', 'Flight3',  # Original flight columns
    ] + rd_columns  # All RD columns
    
    feature_cols = [col for col in feature_df.columns if col not in exclude_cols]
    X = feature_df[feature_cols].copy()
    
    # Encode remaining categorical variables
    categorical_cols = ['origin', 'destination', 'flight', 'class', 'time_of_day', 'duration_category']
    for col in categorical_cols:
        if col in X.columns:
            X[col] = pd.Categorical(X[col]).codes
    
    # Convert boolean to int
    bool_cols = X.select_dtypes(include=['bool']).columns
    X[bool_cols] = X[bool_cols].astype(int)
    
    logger.info(f"Final dataset: {X.shape} features, {len(target)} samples")
    logger.info(f"No RD columns in features: {not any(col.startswith('RD') for col in X.columns)}")
    
    return X, target

if __name__ == "__main__":
    print("No-Leakage Feature Engineering Module")
    print("Creates features without using RD columns to avoid data leakage")
