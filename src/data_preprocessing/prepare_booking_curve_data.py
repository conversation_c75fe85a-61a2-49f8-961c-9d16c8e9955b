"""
预订曲线预测数据准备脚本
基于原始RD时序数据，构建用于预订曲线预测的训练数据
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
import pickle
import json
import os
from pathlib import Path


class BookingCurvePreparer:
    """预订曲线预测数据准备器"""
    
    def __init__(self, data_path: str = "data/"):
        self.data_path = Path(data_path)
        self.products_data = None
        self.schedule_data = None
        self.fleet_data = None
        self.market_share_data = None
        self.unified_dataset = None
        self.booking_curves = {}
        
        # 定义RD时间点（提前期）
        self.rd_timepoints = ['RD0', 'RD1', 'RD2', 'RD4', 'RD6', 'RD7', 'RD8', 'RD11', 
                             'RD14', 'RD16', 'RD18', 'RD21', 'RD24', 'RD28', 'RD33', 
                             'RD39', 'RD44', 'RD49', 'RD59', 'RD69', 'RD79', 'RD89', 
                             'RD99', 'RD119', 'RD139', 'RD159', 'RD179', 'RD209', 
                             'RD239', 'RD269', 'RD330']
        
        # 定义时间窗口（用于聚合）
        self.time_windows = {
            'early': ['RD0', 'RD1', 'RD2', 'RD4', 'RD6'],           # 提前0-6天
            'medium': ['RD7', 'RD8', 'RD11', 'RD14', 'RD16', 'RD18',  # 提前7-28天
                      'RD21', 'RD24', 'RD28'],
            'long': ['RD33', 'RD39', 'RD44', 'RD49', 'RD59', 'RD69',  # 提前29-99天
                    'RD79', 'RD89', 'RD99']
        }
        
        self.cabin_classes = ['B', 'G', 'H', 'K', 'L', 'M', 'Q', 'S', 'T', 'V', 'Y']
        
    def load_all_data(self):
        """加载所有数据源"""
        print("加载数据源...")
        
        # 加载原始数据
        self.products_data = pd.read_csv(self.data_path / "data_fam_products.csv")
        self.schedule_data = pd.read_csv(self.data_path / "data_fam_schedule.csv")
        self.fleet_data = pd.read_csv(self.data_path / "data_fam_fleet.csv")
        self.market_share_data = pd.read_csv(self.data_path / "data_fam_market_share.csv")
        
        # 加载统一基础数据集（用于获取特征）
        unified_path = self.data_path / "processed" / "unified_base_dataset.csv"
        if unified_path.exists():
            self.unified_dataset = pd.read_csv(unified_path)
        else:
            print("警告: 统一基础数据集不存在")
            
        print(f" Products数据: {self.products_data.shape}")
        print(f" Schedule数据: {self.schedule_data.shape}")
        
    def prepare_booking_curve_data(self):
        """准备预订曲线预测数据"""
        print("准备预订曲线预测数据...")
        
        # 1. 构建航班-舱位-时间序列数据
        booking_dataset = self._build_booking_dataset()
        
        # 2. 构建不同预测类型的数据集
        datasets = {}
        
        # 类型1: 完整预订曲线预测 (预测所有33个时间点)
        datasets['full_curve'] = self._prepare_full_curve_prediction(booking_dataset)
        
        # 类型2: 时间窗口聚合预测 (预测3个时间窗口的累计值)
        datasets['window_aggregate'] = self._prepare_window_aggregate_prediction(booking_dataset)
        
        # 类型3: 关键时间点预测 (预测RD0, RD7, RD14, RD21, RD28, RD35)
        datasets['key_points'] = self._prepare_key_points_prediction(booking_dataset)
        
        # 类型4: 最终预订量预测 (基于部分RD数据预测最终RD99)
        datasets['final_demand'] = self._prepare_final_demand_prediction(booking_dataset)
        
        self.booking_curves = datasets
        
    def _build_booking_dataset(self):
        """构建航班-舱位-时间序列数据集"""
        print("构建航班-舱位-时间序列数据集...")
        
        booking_records = []
        
        for _, product in self.products_data.iterrows():
            origin = product['Origin']
            destination = product['Destination']
            flight_id = product['Flight1']
            cabin_class = product['Class']
            fare = product['Fare']
            
            # 获取RD序列数据
            rd_sequence = []
            for rd in self.rd_timepoints:
                rd_value = product.get(rd, 0)
                rd_sequence.append(rd_value)
            
            # 获取航班特征（从统一数据集）
            flight_features = self._get_flight_features(flight_id, origin, destination)
            
            # 构建记录
            booking_record = {
                'origin': origin,
                'destination': destination,
                'flight_id': flight_id,
                'cabin_class': cabin_class,
                'fare': fare,
                'rd_sequence': rd_sequence,
                'total_bookings': sum(rd_sequence),
                **flight_features
            }
            
            booking_records.append(booking_record)
        
        booking_df = pd.DataFrame(booking_records)
        print(f"构建的预订数据集: {booking_df.shape[0]} 行 × {booking_df.shape[1]} 列")
        
        return booking_df
    
    def _get_flight_features(self, flight_id: str, origin: str, destination: str):
        """从统一数据集中获取航班特征"""
        default_features = {
            'flight_duration': 2.0,
            'departure_hour': 12,
            'arrival_hour': 14,
            'departure_period': 'afternoon',
            'arrival_period': 'afternoon', 
            'weekend_flight': 0,
            'dep_offset': 0,
            'arr_offset': 0,
            'route': f"{origin}{destination}",
            'market_share': 0.5,
            'competition_level': 'competitive',
            'is_hub_origin': 0,
            'is_hub_destination': 0,
            'is_hub_to_hub': 0,
            'flight_type': 'direct'
        }
        
        if self.unified_dataset is not None:
            # 尝试匹配航班
            mask = (self.unified_dataset['flight_id'] == flight_id) | \
                   (self.unified_dataset['route'] == f"{origin}{destination}")
            
            if mask.any():
                matched = self.unified_dataset[mask].iloc[0]
                features = {
                    'flight_duration': matched.get('flight_duration', 2.0),
                    'departure_hour': matched.get('departure_hour', 12),
                    'arrival_hour': matched.get('arrival_hour', 14),
                    'departure_period': matched.get('departure_period', 'afternoon'),
                    'arrival_period': matched.get('arrival_period', 'afternoon'),
                    'weekend_flight': int(matched.get('weekend_flight', 0)),
                    'dep_offset': matched.get('dep_offset', 0),
                    'arr_offset': matched.get('arr_offset', 0),
                    'route': matched.get('route', f"{origin}{destination}"),
                    'market_share': matched.get('market_share', 0.5),
                    'competition_level': matched.get('competition_level', 'competitive'),
                    'is_hub_origin': int(matched.get('is_hub_origin', 0)),
                    'is_hub_destination': int(matched.get('is_hub_destination', 0)),
                    'is_hub_to_hub': int(matched.get('is_hub_to_hub', 0)),
                    'flight_type': matched.get('flight_type', 'direct')
                }
                return features
                
        return default_features
    
    def _prepare_full_curve_prediction(self, booking_df: pd.DataFrame):
        """准备完整预订曲线预测数据"""
        print("准备完整预订曲线预测数据...")
        
        # 构建特征矩阵X（航班特征）
        X_rows = []
        y_sequences = []
        
        for _, row in booking_df.iterrows():
            # 静态特征
            static_features = [
                row['flight_duration'],
                row['departure_hour'],
                row['arrival_hour'],
                row['weekend_flight'],
                row['dep_offset'],
                row['arr_offset'],
                row['fare'],
                row['market_share'],
                row['is_hub_origin'],
                row['is_hub_destination'],
                row['is_hub_to_hub']
            ]
            
            # 舱位编码
            cabin_encoded = self.cabin_classes.index(row['cabin_class'])
            static_features.append(cabin_encoded)
            
            X_rows.append(static_features)
            y_sequences.append(row['rd_sequence'])
        
        X = np.array(X_rows)
        y = np.array(y_sequences)
        
        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        # 特征标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # 数据集信息
        dataset_info = {
            "prediction_type": "full_curve",
            "description": "预测全部33个RD时间点的预订量",
            "input_features": 12,
            "output_dimensions": 33,
            "train_samples": len(X_train),
            "test_samples": len(X_test),
            "rd_timepoints": self.rd_timepoints,
            "feature_names": [
                "flight_duration", "departure_hour", "arrival_hour", "weekend_flight",
                "dep_offset", "arr_offset", "fare", "market_share", 
                "is_hub_origin", "is_hub_destination", "is_hub_to_hub", "cabin_class_encoded"
            ]
        }
        
        return {
            'X_train': X_train_scaled,
            'X_test': X_test_scaled,
            'y_train': y_train,
            'y_test': y_test,
            'scaler': scaler,
            'info': dataset_info
        }
    
    def _prepare_window_aggregate_prediction(self, booking_df: pd.DataFrame):
        """准备时间窗口聚合预测数据"""
        print("准备时间窗口聚合预测数据...")
        
        X_rows = []
        y_window_aggregates = []
        
        for _, row in booking_df.iterrows():
            rd_sequence = row['rd_sequence']
            
            # 计算各时间窗口的累计预订量
            early_sum = sum(rd_sequence[:len(self.time_windows['early'])])
            medium_sum = sum(rd_sequence[len(self.time_windows['early']): 
                                      len(self.time_windows['early']) + len(self.time_windows['medium'])])
            long_sum = sum(rd_sequence[len(self.time_windows['early']) + len(self.time_windows['medium']):
                                 len(self.time_windows['early']) + len(self.time_windows['medium']) + len(self.time_windows['long'])])
            
            window_aggregates = [early_sum, medium_sum, long_sum]
            
            # 静态特征（同完整曲线预测）
            static_features = [
                row['flight_duration'],
                row['departure_hour'],
                row['arrival_hour'],
                row['weekend_flight'],
                row['dep_offset'],
                row['arr_offset'],
                row['fare'],
                row['market_share'],
                row['is_hub_origin'],
                row['is_hub_destination'],
                row['is_hub_to_hub']
            ]
            
            cabin_encoded = self.cabin_classes.index(row['cabin_class'])
            static_features.append(cabin_encoded)
            
            X_rows.append(static_features)
            y_window_aggregates.append(window_aggregates)
        
        X = np.array(X_rows)
        y = np.array(y_window_aggregates)
        
        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        # 特征标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        dataset_info = {
            "prediction_type": "window_aggregate",
            "description": "预测早期、中期、长期三个时间窗口的累计预订量",
            "input_features": 12,
            "output_dimensions": 3,
            "train_samples": len(X_train),
            "test_samples": len(X_test),
            "time_windows": self.time_windows,
            "window_names": ["early", "medium", "long"],
            "feature_names": [
                "flight_duration", "departure_hour", "arrival_hour", "weekend_flight",
                "dep_offset", "arr_offset", "fare", "market_share", 
                "is_hub_origin", "is_hub_destination", "is_hub_to_hub", "cabin_class_encoded"
            ]
        }
        
        return {
            'X_train': X_train_scaled,
            'X_test': X_test_scaled,
            'y_train': y_train,
            'y_test': y_test,
            'scaler': scaler,
            'info': dataset_info
        }
    
    def _prepare_key_points_prediction(self, booking_df: pd.DataFrame):
        """准备关键时间点预测数据"""
        print("准备关键时间点预测数据...")
        
        # 定义关键时间点 (使用存在的RD时间点)
        key_rds = ['RD0', 'RD7', 'RD14', 'RD21', 'RD28', 'RD33']
        rd_indices = [self.rd_timepoints.index(rd) for rd in key_rds]
        
        X_rows = []
        y_key_points = []
        
        for _, row in booking_df.iterrows():
            rd_sequence = row['rd_sequence']
            
            # 提取关键时间点的预订量
            key_values = [rd_sequence[idx] for idx in rd_indices]
            
            # 静态特征
            static_features = [
                row['flight_duration'],
                row['departure_hour'],
                row['arrival_hour'],
                row['weekend_flight'],
                row['dep_offset'],
                row['arr_offset'],
                row['fare'],
                row['market_share'],
                row['is_hub_origin'],
                row['is_hub_destination'],
                row['is_hub_to_hub']
            ]
            
            cabin_encoded = self.cabin_classes.index(row['cabin_class'])
            static_features.append(cabin_encoded)
            
            X_rows.append(static_features)
            y_key_points.append(key_values)
        
        X = np.array(X_rows)
        y = np.array(y_key_points)
        
        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        # 特征标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        dataset_info = {
            "prediction_type": "key_points",
            "description": "预测6个关键时间点的预订量",
            "input_features": 12,
            "output_dimensions": 6,
            "train_samples": len(X_train),
            "test_samples": len(X_test),
            "key_timepoints": key_rds,
            "feature_names": [
                "flight_duration", "departure_hour", "arrival_hour", "weekend_flight",
                "dep_offset", "arr_offset", "fare", "market_share", 
                "is_hub_origin", "is_hub_destination", "is_hub_to_hub", "cabin_class_encoded"
            ]
        }
        
        return {
            'X_train': X_train_scaled,
            'X_test': X_test_scaled,
            'y_train': y_train,
            'y_test': y_test,
            'scaler': scaler,
            'info': dataset_info
        }
    
    def _prepare_final_demand_prediction(self, booking_df: pd.DataFrame):
        """准备最终预订量预测数据（基于部分早期数据）"""
        print("准备最终预订量预测数据...")
        
        X_rows = []
        y_final = []
        early_rd_indices = [self.rd_timepoints.index(rd) for rd in self.time_windows['early']]
        
        for _, row in booking_df.iterrows():
            rd_sequence = row['rd_sequence']
            
            # 输入特征：早期预订数据 + 静态特征
            early_bookings = [rd_sequence[idx] for idx in early_rd_indices]
            
            static_features = [
                row['flight_duration'],
                row['departure_hour'],
                row['arrival_hour'],
                row['weekend_flight'],
                row['dep_offset'],
                row['arr_offset'],
                row['fare'],
                row['market_share'],
                row['is_hub_origin'],
                row['is_hub_destination'],
                row['is_hub_to_hub']
            ]
            
            cabin_encoded = self.cabin_classes.index(row['cabin_class'])
            static_features.append(cabin_encoded)
            
            # 完整输入特征（早期预订 + 静态特征）
            full_features = early_bookings + static_features
            
            # 目标变量：RD99的最终预订量
            target = rd_sequence[self.rd_timepoints.index('RD99')]
            
            X_rows.append(full_features)
            y_final.append(target)
        
        X = np.array(X_rows)
        y = np.array(y_final)
        
        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        # 特征标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        feature_names = [f"early_{rd}" for rd in self.time_windows['early']] + [
            "flight_duration", "departure_hour", "arrival_hour", "weekend_flight",
            "dep_offset", "arr_offset", "fare", "market_share", 
            "is_hub_origin", "is_hub_destination", "is_hub_to_hub", "cabin_class_encoded"
        ]
        
        dataset_info = {
            "prediction_type": "final_demand",
            "description": "基于早期预订数据预测最终(RD99)预订量",
            "input_features": len(feature_names),
            "output_dimensions": 1,
            "train_samples": len(X_train),
            "test_samples": len(X_test),
            "early_rds_used": self.time_windows['early'],
            "target_rd": "RD99",
            "feature_names": feature_names
        }
        
        return {
            'X_train': X_train_scaled,
            'X_test': X_test_scaled,
            'y_train': y_train,
            'y_test': y_test,
            'scaler': scaler,
            'info': dataset_info
        }
    
    def save_datasets(self):
        """保存所有预订曲线预测数据集"""
        print("保存预订曲线预测数据集...")
        
        output_path = self.data_path / "processed" / "booking_curve"
        output_path.mkdir(exist_ok=True)
        
        summary = {}
        
        for dataset_name, dataset in self.booking_curves.items():
            print(f"保存 {dataset_name} 数据集...")
            
            dataset_path = output_path / dataset_name
            dataset_path.mkdir(exist_ok=True)
            
            # 保存数据文件
            np.save(dataset_path / f"{dataset_name}_X_train.npy", dataset['X_train'])
            np.save(dataset_path / f"{dataset_name}_X_test.npy", dataset['X_test'])
            np.save(dataset_path / f"{dataset_name}_y_train.npy", dataset['y_train'])
            np.save(dataset_path / f"{dataset_name}_y_test.npy", dataset['y_test'])
            
            # 保存预处理器
            with open(dataset_path / f"{dataset_name}_scaler.pkl", 'wb') as f:
                pickle.dump(dataset['scaler'], f)
            
            # 保存数据集信息
            with open(dataset_path / f"{dataset_name}_dataset_info.json", 'w', encoding='utf-8') as f:
                json.dump(dataset['info'], f, ensure_ascii=False, indent=2)
            
            summary[dataset_name] = dataset['info']
        
        # 保存总体汇总
        with open(output_path / "booking_curve_summary.json", 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
            
        print(f"所有数据集已保存到: {output_path}")
    
    def generate_summary_report(self):
        """生成预订曲线数据准备总结报告"""
        print("\n" + "="*50)
        print("预订曲线预测数据准备总结报告")
        print("="*50)
        
        report_lines = []
        report_lines.append("# 预订曲线预测数据准备报告\n")
        report_lines.append(f"## 概述\n")
        report_lines.append(f"预订曲线预测基于原始RD时序数据，构建了4种不同类型的预测任务：\n")
        
        total_samples = 0
        total_features = 0
        
        for dataset_name, dataset in self.booking_curves.items():
            info = dataset['info']
            total_samples += info['train_samples'] + info['test_samples']
            
            report_lines.append(f"### {dataset_name.upper()} - {info['description']}\n")
            report_lines.append(f"- **输入特征**: {info['input_features']} 维")
            report_lines.append(f"- **输出维度**: {info['output_dimensions']} 维")
            report_lines.append(f"- **训练样本**: {info['train_samples']}")
            report_lines.append(f"- **测试样本**: {info['test_samples']}")
            
            if 'rd_timepoints' in info:
                report_lines.append(f"- **时间点数**: {len(info['rd_timepoints'])}")
            if 'window_names' in info:
                report_lines.append(f"- **时间窗口**: {', '.join(info['window_names'])}")
            if 'key_timepoints' in info:
                report_lines.append(f"- **关键时间点**: {', '.join(info['key_timepoints'])}")
            if 'early_rds_used' in info:
                report_lines.append(f"- **输入RD**: {', '.join(info['early_rds_used'])}")
                report_lines.append(f"- **预测目标**: {info['target_rd']}")
                
            report_lines.append("")
        
        report_lines.append(f"## 数据源分析\n")
        report_lines.append(f"- **原始产品数据**: {len(self.products_data):,} 条记录")
        report_lines.append(f"- **舱位等级**: {len(self.cabin_classes)} 个")
        report_lines.append(f"- **RD时间点**: {len(self.rd_timepoints)} 个")
        report_lines.append(f"- **时间跨度**: RD0 到 RD330 (提前0天到330天)\n")
        
        report_lines.append(f"## 总体统计\n")
        report_lines.append(f"- **预测任务数**: {len(self.booking_curves)}")
        report_lines.append(f"- **总样本数**: {total_samples:,}")
        report_lines.append(f"- **平均特征数**: {np.mean([d['info']['input_features'] for d in self.booking_curves.values()]):.0f}\n")
        
        report_lines.append(f"## 预测任务适用场景\n")
        
        scenarios = {
            "full_curve": "适合需要完整预订曲线的场景，如收益管理系统的动态定价",
            "window_aggregate": "适合需要预测预订进度的场景，如运力调配决策",
            "key_points": "适合关注特定时间点的场景，如营销活动效果评估",
            "final_demand": "适合基于早期数据预测最终需求的场景，如初步运力规划"
        }
        
        for task, scenario in scenarios.items():
            info = self.booking_curves[task]['info']
            report_lines.append(f"### {task.upper()}\n")
            report_lines.append(f"{scenario}\n")
        
        report_lines.append("## 技术特点\n")
        report_lines.append("- **数据标准化**: 使用Z-score标准化处理数值特征\n")
        report_lines.append("- **一致性分割**: 所有数据集使用相同的随机种子(42)确保一致性\n")
        report_lines.append("- **特征工程**: 综合静态特征和动态特征\n")
        report_lines.append("- **多任务学习**: 支持不同粒度的预测任务\n")
        
        report_lines.append("## 文件结构\n")
        report_lines.append("```\ndata/processed/booking_curve/\n")
        for dataset_name in sorted(self.booking_curves.keys()):
            report_lines.append(f"├── {dataset_name}/\n")
            report_lines.append(f"│   ├── {dataset_name}_X_train.npy     # 训练特征\n")
            report_lines.append(f"│   ├── {dataset_name}_X_test.npy      # 测试特征\n")
            report_lines.append(f"│   ├── {dataset_name}_y_train.npy     # 训练目标\n")
            report_lines.append(f"│   ├── {dataset_name}_y_test.npy      # 测试目标\n")
            report_lines.append(f"│   ├── {dataset_name}_scaler.pkl      # 标准化器\n")
            report_lines.append(f"│   └── {dataset_name}_dataset_info.json # 数据集信息\n")
        report_lines.append("└── booking_curve_summary.json           # 总体汇总\n")
        report_lines.append("```\n")
        
        report_lines.append("---\n")
        report_lines.append(f"*生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}*")
        report_lines.append(f"*作者: Claude for IE2025项目*")
        
        # 保存报告
        report_content = "\n".join(report_lines)
        with open("results/booking_curve_data_preparation_report.md", "w", encoding="utf-8") as f:
            f.write(report_content)
        
        # 打印报告
        print("\n".join(report_lines[:30]))
        
        return report_content


def main():
    """主函数"""
    print("开始预订曲线预测数据准备...")
    
    # 创建数据准备器
    preparer = BookingCurvePreparer()
    
    # 加载数据
    preparer.load_all_data()
    
    # 准备预测数据
    preparer.prepare_booking_curve_data()
    
    # 保存数据集
    preparer.save_datasets()
    
    # 生成总结报告
    report = preparer.generate_summary_report()
    
    print("\n预订曲线预测数据准备完成!")
    return preparer


if __name__ == "__main__":
    preparer = main()