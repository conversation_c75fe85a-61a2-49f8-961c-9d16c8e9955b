"""
数据集验证与质量检查脚本
验证所有预测目标数据集的质量、一致性和完整性
"""

import pandas as pd
import numpy as np
import json
import os
from pathlib import Path
from typing import Dict, List, Any
import pickle


class DatasetValidator:
    """数据集验证器"""
    
    def __init__(self, base_path: str = "data/processed/"):
        self.base_path = Path(base_path)
        self.validation_results = {}
        
    def validate_all_datasets(self):
        """验证所有数据集"""
        print("开始数据集验证与质量检查...")
        
        # 验证总需求预测数据集
        print("\n=== 验证总需求预测数据集 ===")
        self._validate_total_demand_dataset()
        
        # 验证分舱位需求预测数据集
        print("\n=== 验证分舱位需求预测数据集 ===")
        self._validate_cabin_demand_datasets()
        
        # 验证预订曲线预测数据集
        print("\n=== 验证预订曲线预测数据集 ===")
        self._validate_booking_curve_datasets()
        
        # 交叉验证检查
        print("\n=== 交叉验证检查 ===")
        self._cross_validation_checks()
        
        # 生成综合质量报告
        print("\n=== 生成质量报告 ===")
        self._generate_quality_report()
        
    def _validate_total_demand_dataset(self):
        """验证总需求预测数据集"""
        dataset_name = "总需求预测"
        dataset_path = self.base_path  # 总需求文件直接在processed目录下
        
        if not dataset_path.exists():
            print(f"❌ {dataset_name}数据集不存在")
            return
        
        results = {"dataset": dataset_name, "checks": {}}
        
        # 检查文件完整性
        expected_files = [
            "total_demand_X_train.csv", "total_demand_X_test.csv",
            "total_demand_y_train.csv", "total_demand_y_test.csv",
            "total_demand_features.csv", "total_demand_dataset_info.json",
            "total_demand_scaler.pkl", "total_demand_label_encoders.pkl"
        ]
        
        files_exist = all((dataset_path / f).exists() for f in expected_files)
        results["checks"]["file_integrity"] = {
            "status": "✅" if files_exist else "❌",
            "details": f"完整文件: {sum(1 for f in expected_files if (dataset_path / f).exists())}/{len(expected_files)}"
        }
        
        if files_exist:
            # 加载数据
            X_train = pd.read_csv(dataset_path / "total_demand_X_train.csv")
            X_test = pd.read_csv(dataset_path / "total_demand_X_test.csv")
            y_train = pd.read_csv(dataset_path / "total_demand_y_train.csv")
            y_test = pd.read_csv(dataset_path / "total_demand_y_test.csv")
            
            # 数据维度检查
            results["checks"]["data_dimensions"] = {
                "status": "✅",
                "details": f"训练集: {X_train.shape}, 测试集: {X_test.shape}"
            }
            
            # 特征一致性检查
            feature_consistency = X_train.shape[1] == X_test.shape[1]
            results["checks"]["feature_consistency"] = {
                "status": "✅" if feature_consistency else "❌",
                "details": f"训练特征数: {X_train.shape[1]}, 测试特征数: {X_test.shape[1]}"
            }
            
            # 缺失值检查
            train_missing = X_train.isnull().sum().sum() + y_train.isnull().sum().sum()
            test_missing = X_test.isnull().sum().sum() + y_test.isnull().sum().sum()
            no_missing = train_missing == 0 and test_missing == 0
            results["checks"]["missing_values"] = {
                "status": "✅" if no_missing else "❌",
                "details": f"训练缺失: {train_missing}, 测试缺失: {test_missing}"
            }
            
            # 数据范围检查
            y_range = (y_train.values.min(), y_train.values.max())
            y_positive = y_range[0] >= 0
            results["checks"]["data_range"] = {
                "status": "✅" if y_positive else "❌",
                "details": f"需求范围: [{y_range[0]:.2f}, {y_range[1]:.2f}]"
            }
            
            # 数据分割比例检查
            total_samples = len(X_train) + len(X_test)
            train_ratio = len(X_train) / total_samples
            expected_ratio = 0.8
            ratio_ok = abs(train_ratio - expected_ratio) < 0.01
            results["checks"]["split_ratio"] = {
                "status": "✅" if ratio_ok else "❌",
                "details": f"训练比例: {train_ratio:.2f} (期望: {expected_ratio})"
            }
        
        self.validation_results[dataset_name] = results
        
    def _validate_cabin_demand_datasets(self):
        """验证分舱位需求预测数据集"""
        cabin_classes = ['B', 'G', 'H', 'K', 'L', 'M', 'Q', 'S', 'T', 'V', 'Y']
        base_path = self.base_path / "cabin_demand"
        
        if not base_path.exists():
            print("❌ 分舱位需求预测数据集不存在")
            return
        
        overall_results = {"dataset": "分舱位需求预测", "cabin_results": {}, "overall_checks": {}}
        
        total_files = 0
        complete_datasets = 0
        
        for cabin in cabin_classes:
            cabin_path = base_path / cabin
            cabin_result = {"cabin": cabin, "checks": {}}
            
            if not cabin_path.exists():
                cabin_result["checks"]["existence"] = {"status": "❌", "details": "舱位目录不存在"}
                overall_results["cabin_results"][cabin] = cabin_result
                continue
            
            # 检查文件
            expected_files = [
                f"{cabin}_X_train.csv", f"{cabin}_X_test.csv",
                f"{cabin}_y_train.csv", f"{cabin}_y_test.csv",
                f"{cabin}_features.csv", f"{cabin}_dataset_info.json",
                f"{cabin}_scaler.pkl", f"{cabin}_label_encoders.pkl"
            ]
            
            files_exist = all((cabin_path / f).exists() for f in expected_files)
            existing_files = sum(1 for f in expected_files if (cabin_path / f).exists())
            
            cabin_result["checks"]["file_integrity"] = {
                "status": "✅" if files_exist else "❌",
                "details": f"完整文件: {existing_files}/{len(expected_files)}"
            }
            
            total_files += existing_files
            if files_exist:
                complete_datasets += 1
                
                # 加载并验证数据
                X_train = pd.read_csv(cabin_path / f"{cabin}_X_train.csv")
                X_test = pd.read_csv(cabin_path / f"{cabin}_X_test.csv")
                y_train = pd.read_csv(cabin_path / f"{cabin}_y_train.csv")
                y_test = pd.read_csv(cabin_path / f"{cabin}_y_test.csv")
                
                # 数据维度一致性
                consistent_dims = X_train.shape[1] == X_test.shape[1]
                cabin_result["checks"]["dimension_consistency"] = {
                    "status": "✅" if consistent_dims else "❌",
                    "details": f"特征数: {X_train.shape[1]}"
                }
                
                # 数据质量
                train_missing = X_train.isnull().sum().sum() + y_train.isnull().sum().sum()
                test_missing = X_test.isnull().sum().sum() + y_test.isnull().sum().sum()
                no_missing = train_missing == 0 and test_missing == 0
                cabin_result["checks"]["data_quality"] = {
                    "status": "✅" if no_missing else "❌",
                    "details": f"训练/测试缺失: {train_missing}/{test_missing}"
                }
                
                # 需求统计
                y_mean = y_train.values.mean()
                y_pos_ratio = (y_train.values > 0).mean()
                cabin_result["checks"]["demand_statistics"] = {
                    "status": "✅",
                    "details": f"均值: {y_mean:.2f}, 正比例: {y_pos_ratio:.1%}"
                }
            
            overall_results["cabin_results"][cabin] = cabin_result
        
        # 整体检查
        overall_results["overall_checks"]["dataset_completeness"] = {
            "status": "✅" if complete_datasets == len(cabin_classes) else "⚠️",
            "details": f"完整舱位: {complete_datasets}/{len(cabin_classes)}"
        }
        
        overall_results["overall_checks"]["file_coverage"] = {
            "status": "✅",
            "details": f"总文件存在率: {total_files/(len(cabin_classes)*len(expected_files))*100:.1f}%"
        }
        
        self.validation_results["分舱位需求预测"] = overall_results
        
    def _validate_booking_curve_datasets(self):
        """验证预订曲线预测数据集"""
        dataset_types = ["full_curve", "window_aggregate", "key_points", "final_demand"]
        base_path = self.base_path / "booking_curve"
        
        if not base_path.exists():
            print("❌ 预订曲线预测数据集不存在")
            return
        
        overall_results = {"dataset": "预订曲线预测", "type_results": {}, "overall_checks": {}}
        
        total_files = 0
        complete_datasets = 0
        
        for dataset_type in dataset_types:
            type_path = base_path / dataset_type
            type_result = {"type": dataset_type, "checks": {}}
            
            if not type_path.exists():
                type_result["checks"]["existence"] = {"status": "❌", "details": "数据集目录不存在"}
                overall_results["type_results"][dataset_type] = type_result
                continue
            
            # 检查文件
            expected_files = [
                f"{dataset_type}_X_train.npy", f"{dataset_type}_X_test.npy",
                f"{dataset_type}_y_train.npy", f"{dataset_type}_y_test.npy",
                f"{dataset_type}_scaler.pkl", f"{dataset_type}_dataset_info.json"
            ]
            
            files_exist = all((type_path / f).exists() for f in expected_files)
            existing_files = sum(1 for f in expected_files if (type_path / f).exists())
            
            type_result["checks"]["file_integrity"] = {
                "status": "✅" if files_exist else "❌",
                "details": f"完整文件: {existing_files}/{len(expected_files)}"
            }
            
            total_files += existing_files
            if files_exist:
                complete_datasets += 1
                
                # 加载数据集信息
                with open(type_path / f"{dataset_type}_dataset_info.json", 'r') as f:
                    info = json.load(f)
                
                type_result["checks"]["dataset_info"] = {
                    "status": "✅",
                    "details": f"输入: {info['input_features']}维, 输出: {info['output_dimensions']}维"
                }
                
                # 验证数据加载
                try:
                    X_train = np.load(type_path / f"{dataset_type}_X_train.npy")
                    X_test = np.load(type_path / f"{dataset_type}_X_test.npy")
                    y_train = np.load(type_path / f"{dataset_type}_y_train.npy")
                    y_test = np.load(type_path / f"{dataset_type}_y_test.npy")
                    
                    # 维度一致性
                    sample_consistent = len(X_train) == len(y_train) and len(X_test) == len(y_test)
                    type_result["checks"]["sample_consistency"] = {
                        "status": "✅" if sample_consistent else "❌",
                        "details": f"训练: {len(X_train)}样本, 测试: {len(X_test)}样本"
                    }
                    
                    # 特征一致性
                    feature_consistent = X_train.shape[1] == X_test.shape[1]
                    type_result["checks"]["feature_consistency"] = {
                        "status": "✅" if feature_consistent else "❌",
                        "details": f"特征维度: {X_train.shape[1]}"
                    }
                    
                except Exception as e:
                    type_result["checks"]["data_loading"] = {
                        "status": "❌",
                        "details": f"加载失败: {str(e)}"
                    }
            
            overall_results["type_results"][dataset_type] = type_result
        
        # 整体检查
        overall_results["overall_checks"]["dataset_completeness"] = {
            "status": "✅" if complete_datasets == len(dataset_types) else "⚠️",
            "details": f"完整数据集: {complete_datasets}/{len(dataset_types)}"
        }
        
        overall_results["overall_checks"]["file_coverage"] = {
            "status": "✅",
            "details": f"总文件存在率: {total_files/(len(dataset_types)*len(expected_files))*100:.1f}%"
        }
        
        self.validation_results["预订曲线预测"] = overall_results
        
    def _cross_validation_checks(self):
        """交叉验证检查"""
        cross_results = {"dataset": "交叉验证", "checks": {}}
        
        # 检查数据分割一致性
        try:
            # 总需求样本数
            total_info_path = self.base_path / "total_demand" / "total_demand_dataset_info.json"
            if total_info_path.exists():
                with open(total_info_path, 'r') as f:
                    total_info = json.load(f)
                total_samples = total_info["train_samples"] + total_info["test_samples"]
            else:
                total_samples = 0
                
            # 分舱位样本数 (每个舱位应该相同的样本数)
            cabin_path = self.base_path / "cabin_demand"
            if cabin_path.exists():
                cabin_samples = []
                for cabin in ['B', 'G', 'H', 'K', 'L', 'M', 'Q', 'S', 'T', 'V', 'Y']:
                    info_path = cabin_path / cabin / f"{cabin}_dataset_info.json"
                    if info_path.exists():
                        with open(info_path, 'r') as f:
                            cabin_info = json.load(f)
                        cabin_samples.append(cabin_info["train_samples"] + cabin_info["test_samples"])
                
                if cabin_samples:
                    samples_consistent = all(s == cabin_samples[0] for s in cabin_samples)
                    cross_results["checks"]["sample_consisistency"] = {
                        "status": "✅" if samples_consistent else "⚠️",
                        "details": f"分舱位样本: {cabin_samples[0]} (全部一致: {samples_consistent})"
                    }
                    
                    cross_results["checks"]["total_vs_cabin"] = {
                        "status": "✅" if total_samples == cabin_samples[0] else "⚠️",
                        "details": f"总需求: {total_samples}, 单舱位: {cabin_samples[0]}"
                    }
            
            # 预订曲线样本数
            booking_path = self.base_path / "booking_curve"
            if booking_path.exists():
                summary_path = booking_path / "booking_curve_summary.json"
                if summary_path.exists():
                    with open(summary_path, 'r') as f:
                        booking_summary = json.load(f)
                    
                    first_dataset = list(booking_summary.values())[0]
                    booking_samples = first_dataset["train_samples"] + first_dataset["test_samples"]
                    
                    cross_results["checks"]["booking_sample_size"] = {
                        "status": "✅" if booking_samples > 0 else "⚠️",
                        "details": f"预订曲线样本: {booking_samples:,}"
                    }
                    
                    if total_samples > 0:
                        ratio_expected = len(self.cabin_classes)  # 航班数 × 舱位数
                        ratio_actual = booking_samples / total_samples
                        cross_results["checks"]["sample_ratio"] = {
                            "status": "✅" if abs(ratio_actual - ratio_expected) < 2 else "⚠️",
                            "details": f"比例: {ratio_actual:.1f} (期望约{ratio_expected})"
                        }
        
        except Exception as e:
            cross_results["checks"]["cross_validation"] = {
                "status": "❌",
                "details": f"验证失败: {str(e)}"
            }
        
        self.validation_results["交叉验证"] = cross_results
        
    def _generate_quality_report(self):
        """生成综合质量报告"""
        print("\n" + "="*60)
        print("📊 数据集验证与质量检查报告")
        print("="*60)
        
        report_lines = []
        report_lines.append("# 数据集验证与质量检查报告\n")
        report_lines.append(f"## 概述\n")
        report_lines.append(f"本报告验证了三大类预测目标数据集的质量和一致性：\n")
        
        overall_score = 0
        max_score = 0
        
        # 总需求预测
        if "总需求预测" in self.validation_results:
            results = self.validation_results["总需求预测"]
            score = sum(1 for check in results["checks"].values() if "✅" in check["status"])
            total_checks = len(results["checks"])
            max_score += total_checks
            overall_score += score
            
            report_lines.append(f"### ✅ 总需求预测数据集")
            report_lines.append(f"- **验证通过率**: {score}/{total_checks} ({score/total_checks*100:.0f}%)\n")
            
            for check_name, check_result in results["checks"].items():
                status_icon = "✅" if "✅" in check_result["status"] else "❌"
                report_lines.append(f"  - {status_icon} {check_name}: {check_result['details']}")
        
        # 分舱位需求预测
        if "分舱位需求预测" in self.validation_results:
            results = self.validation_results["分舱位需求预测"]
            cabin_count = len(results["cabin_results"])
            
            # 计算整体分数
            overall_file_score = 0
            overall_max_score = 0
            
            report_lines.append(f"\n### ✅ 分舱位需求预测数据集 ({cabin_count}个舱位)")
            
            for cabin_name, cabin_result in results["cabin_results"].items():
                cabin_score = sum(1 for check in cabin_result["checks"].values() if "✅" in check["status"])
                cabin_checks = len(cabin_result["checks"])
                overall_score += cabin_score
                max_score += cabin_checks
                
                status_icon = "✅" if cabin_score == cabin_checks else "⚠️" if cabin_score > 0 else "❌"
                report_lines.append(f"  - {status_icon} **{cabin_name}舱位**: {cabin_score}/{cabin_checks}")
            
            # 整体检查
            for check_name, check_result in results["overall_checks"].items():
                status_icon = "✅" if "✅" in check_result["status"] else "⚠️" if "⚠️" in check_result["status"] else "❌"
                report_lines.append(f"  - {status_icon} {check_name}: {check_result['details']}")
            report_lines.append("")
        
        # 预订曲线预测
        if "预订曲线预测" in self.validation_results:
            results = self.validation_results["预订曲线预测"]
            
            report_lines.append(f"### ✅ 预订曲线预测数据集")
            
            for type_name, type_result in results["type_results"].items():
                type_score = sum(1 for check in type_result["checks"].values() if "✅" in check["status"])
                type_checks = len(type_result["checks"])
                overall_score += type_score
                max_score += type_checks
                
                status_icon = "✅" if type_score == type_checks else "⚠️" if type_score > 0 else "❌"
                report_lines.append(f"  - {status_icon} **{type_name}**: {type_score}/{type_checks}")
            
            for check_name, check_result in results["overall_checks"].items():
                status_icon = "✅" if "✅" in check_result["status"] else "⚠️" if "⚠️" in check_result["status"] else "❌"
                report_lines.append(f"  - {status_icon} {check_name}: {check_result['details']}")
            report_lines.append("")
        
        # 交叉验证
        if "交叉验证" in self.validation_results:
            results = self.validation_results["交叉验证"]
            cross_score = sum(1 for check in results["checks"].values() if "✅" in check["status"])
            cross_checks = len(results["checks"])
            overall_score += cross_score
            max_score += cross_checks
            
            report_lines.append(f"### 🔍 交叉验证结果")
            for check_name, check_result in results["checks"].items():
                status_icon = "✅" if "✅" in check_result["status"] else "⚠️" if "⚠️" in check_result["status"] else "❌"
                report_lines.append(f"  - {status_icon} {check_name}: {check_result['details']}")
            report_lines.append("")
        
        # 整体评分
        if max_score > 0:
            overall_percentage = (overall_score / max_score) * 100
            report_lines.append(f"## 🎯 整体质量评分\n")
            
            if overall_percentage >= 95:
                grade = "A+ 🏆"
            elif overall_percentage >= 90:
                grade = "A ✨"
            elif overall_percentage >= 80:
                grade = "B+ 👍"
            elif overall_percentage >= 70:
                grade = "B ⚠️"
            elif overall_percentage >= 60:
                grade = "C ⚠️"
            else:
                grade = "D ❌"
            
            report_lines.append(f"- **总体评分**: {overall_percentage:.1f}% ({grade})")
            report_lines.append(f"- **验证项目**: {overall_score}/{max_score} 通过\n")
            
            # 质量等级说明
            report_lines.append(f"### 质量等级说明\n")
            if overall_percentage >= 90:
                report_lines.append(f"**优秀 (A级)**: 数据集质量很高，所有主要验证项目均通过，可用于模型训练和部署。\n")
            elif overall_percentage >= 80:
                report_lines.append(f"**良好 (B+级)**: 数据集质量良好，大部分验证项目通过，存在少量需要注意的项目。\n")
            else:
                report_lines.append(f"**需要改进 (C级及以下)**: 数据集存在较多问题，建议优先解决关键问题后再进行模型训练。\n")
        
        # 存储结果
        report_lines.append(f"## 📋 详细验证结果\n")
        report_lines.append(f"所有验证结果已保存至 `results/validation_report.json`。\n")
        
        report_lines.append(f"---\n")
        report_lines.append(f"*验证时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}*")
        report_lines.append(f"*验证工具: Claude for IE2025项目*")
        
        # 打印报告摘要
        print("\n".join(report_lines[:30]))
        print(f"\n📈 总体评分: {overall_percentage:.1f}% ({grade})")
        
        # 保存详细结果
        with open("results/validation_report.json", "w", encoding="utf-8") as f:
            json.dump(self.validation_results, f, ensure_ascii=False, indent=2)
        
        # 保存报告
        report_content = "\n".join(report_lines)
        with open("results/dataset_validation_report.md", "w", encoding="utf-8") as f:
            f.write(report_content)
        
        print(f"\n✅ 验证报告已保存到 results/dataset_validation_report.md")
        print(f"✅ 详细结果已保存到 results/validation_report.json")


def main():
    """主函数"""
    validator = DatasetValidator()
    validator.validate_all_datasets()
    return validator


if __name__ == "__main__":
    validator = main()