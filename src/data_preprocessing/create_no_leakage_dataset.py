"""
Simple No-Leakage Feature Engineering for Total Demand Prediction
Creates safe features without any RD-based statistics
"""
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
import joblib
import os

def create_simple_no_leakage_dataset():
    """
    创建简化的无泄漏数据集
    
    只使用最基础、最安全的特征：
    1. 航班时刻表特征
    2. 航线网络特征  
    3. 市场竞争特征
    
    完全避免使用任何products数据中的RD相关统计
    """
    print("🛡️ 创建简化无泄漏数据集...")
    
    # 数据路径
    data_path = "/home/<USER>/cem208/code/ie2025/data"
    processed_path = "/home/<USER>/cem208/code/ie2025/data/processed"
    
    # 加载数据
    print("📂 加载数据...")
    schedule_data = pd.read_csv(os.path.join(data_path, "data_fam_schedule.csv"))
    market_share_data = pd.read_csv(os.path.join(data_path, "data_fam_market_share.csv"))
    products_data = pd.read_csv(os.path.join(data_path, "data_fam_products.csv"))
    
    print(f"📊 数据规模:")
    print(f"  - 航班时刻表: {schedule_data.shape}")
    print(f"  - 市场份额: {market_share_data.shape}")
    print(f"  - 产品数据: {products_data.shape}")
    
    # 计算总需求标签 (只用于训练，不用于特征)
    print("🎯 计算总需求标签...")
    demand_labels = {}
    rd_columns = [col for col in products_data.columns if col.startswith('RD')]
    
    for _, flight in schedule_data.iterrows():
        flight_id = flight['flight']
        
        # 在products数据中查找对应的航班 (可能在Flight1, Flight2, Flight3列)
        flight_products = products_data[
            (products_data['Flight1'] == flight_id) | 
            (products_data['Flight2'] == flight_id) | 
            (products_data['Flight3'] == flight_id)
        ]
        
        if len(flight_products) > 0:
            # 计算该航班的总需求（所有舱位+所有RD点的总和）
            total_demand = flight_products[rd_columns].values.sum()
            demand_labels[flight_id] = total_demand
        else:
            demand_labels[flight_id] = 0
    
    # 创建安全特征
    print("🛡️ 创建安全特征...")
    safe_features = []
    
    # 识别枢纽机场
    departure_counts = schedule_data['origin'].value_counts()
    arrival_counts = schedule_data['destination'].value_counts()
    total_counts = departure_counts.add(arrival_counts, fill_value=0)
    hub_airports = total_counts.nlargest(10).index.tolist()
    print(f"🏆 枢纽机场: {hub_airports}")
    
    for _, flight in schedule_data.iterrows():
        flight_id = flight['flight']
        origin = flight['origin']
        destination = flight['destination']
        route = f"{origin}-{destination}"
        
        features = {}
        
        # === 基础时间特征 ===
        features['departure_time'] = flight['depoff']
        features['arrival_time'] = flight['arroff']
        features['departure_hour'] = flight['deptime'] % 24
        features['arrival_hour'] = flight['arrtime'] % 24
        features['flight_duration'] = flight['arroff'] - flight['depoff']
        
        # === 机场网络特征 ===
        features['is_hub_origin'] = 1 if origin in hub_airports else 0
        features['is_hub_destination'] = 1 if destination in hub_airports else 0
        features['is_hub_to_hub'] = 1 if (origin in hub_airports and destination in hub_airports) else 0
        features['same_airport'] = 1 if origin == destination else 0
        features['hub_connection_score'] = features['is_hub_origin'] + features['is_hub_destination']
        
        # === 航线特征 ===
        features['route'] = route
        
        # 获取市场份额
        market_share_row = market_share_data[
            (market_share_data['Org'] == origin) & 
            (market_share_data['Des'] == destination)
        ]
        
        market_share = market_share_row['Host_share'].iloc[0] if len(market_share_row) > 0 else 0.5
        features['market_share'] = market_share
        
        # 市场竞争等级
        if market_share >= 0.8:
            competition_level = 3  # monopoly
        elif market_share >= 0.5:
            competition_level = 2  # dominant
        elif market_share >= 0.2:
            competition_level = 1  # competitive
        else:
            competition_level = 0  # weak
        
        features['competition_level'] = competition_level
        features['competition_score'] = market_share
        
        # === 航线频率特征 ===
        route_stats = schedule_data[
            (schedule_data['origin'] == origin) & 
            (schedule_data['destination'] == destination)
        ]
        features['route_frequency'] = len(route_stats)
        
        # === 其他安全特征 ===
        features['flight_id'] = flight_id
        features['total_demand'] = demand_labels[flight_id]
        
        safe_features.append(features)
    
    # 创建DataFrame
    feature_df = pd.DataFrame(safe_features)
    
    # 移除缺失值的行
    feature_df = feature_df.dropna()
    
    print(f"🔍 数据完整性:")
    print(f"  - 原始样本数: {len(safe_features)}")
    print(f"  - 清洁后样本数: {len(feature_df)}")
    print(f"  - 特征数: {len([col for col in feature_df.columns if col not in ['flight_id', 'route', 'total_demand']])}")
    
    # 检查需求标签
    print(f"📊 需求统计:")
    print(f"  - 范围: {feature_df['total_demand'].min():.2f} - {feature_df['total_demand'].max():.2f}")
    print(f"  - 均值: {feature_df['total_demand'].mean():.2f}")
    print(f"  - 标准差: {feature_df['total_demand'].std():.2f}")
    
    # 准备训练数据
    print("🔄 准备训练数据...")
    
    # 选择数值特征
    numeric_features = [
        'departure_time', 'arrival_time', 'departure_hour', 'arrival_hour', 'flight_duration',
        'is_hub_origin', 'is_hub_destination', 'is_hub_to_hub', 'same_airport', 'hub_connection_score',
        'market_share', 'competition_level', 'competition_score', 'route_frequency'
    ]
    
    X = feature_df[numeric_features]
    y = feature_df['total_demand']
    
    # 标准化特征
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    X_scaled = pd.DataFrame(X_scaled, columns=numeric_features)
    
    # 划分训练测试集
    X_train, X_test, y_train, y_test = train_test_split(
        X_scaled, y, test_size=0.2, random_state=42
    )
    
    print(f"📊 数据划分:")
    print(f"  - 训练集: {X_train.shape[0]} 样本")
    print(f"  - 测试集: {X_test.shape[0]} 样本")
    print(f"  - 特征数: {X_train.shape[1]}")
    
    # 保存数据
    print("💾 保存数据...")
    
    # 保存特征数据
    X_train.to_csv(os.path.join(processed_path, "total_demand_X_train_no_leakage.csv"), index=False)
    X_test.to_csv(os.path.join(processed_path, "total_demand_X_test_no_leakage.csv"), index=False)
    
    # 保存标签数据
    y_train.to_csv(os.path.join(processed_path, "total_demand_y_train_no_leakage.csv"), index=False)
    y_test.to_csv(os.path.join(processed_path, "total_demand_y_test_no_leakage.csv"), index=False)
    
    # 保存预处理器
    joblib.dump(scaler, os.path.join(processed_path, "total_demand_scaler_no_leakage.pkl"))
    
    # 保存特征信息
    feature_info = pd.DataFrame({
        'feature_name': numeric_features,
        'feature_type': ['numeric'] * len(numeric_features)
    })
    feature_info.to_csv(os.path.join(processed_path, "total_demand_features_no_leakage.csv"), index=False)
    
    # 验证无泄漏风险
    print("🛡️ 验证无泄漏风险...")
    
    # 检查特征与标签的相关性
    correlations = {}
    for feature in numeric_features:
        corr = X_train[feature].corr(y_train)
        correlations[feature] = corr
    
    print("📈 特征与标签相关性:")
    for feature, corr in correlations.items():
        print(f"  - {feature}: {corr:.4f}")
    
    # 检查是否存在异常高的相关性
    high_corr_features = [f for f, c in correlations.items() if abs(corr) > 0.8]
    if high_corr_features:
        print(f"⚠️  发现高相关性特征: {high_corr_features}")
    else:
        print("✅ 未发现异常高相关性，无泄漏风险低")
    
    print("\n✅ 无泄漏数据集创建完成!")
    print("🛡️ 特征集完全安全，可用于真实的预测模型训练!")

if __name__ == "__main__":
    create_simple_no_leakage_dataset()