{"target_variable": "total_rd_bookings", "n_features": 102, "train_samples": 652, "test_samples": 163, "features": ["departure_time", "arrival_time", "departure_hour", "arrival_hour", "flight_duration", "dep_offset", "arr_offset", "market_share", "is_hub_origin", "is_hub_destination", "is_hub_to_hub", "same_airport", "is_multi_stop", "class_B_total", "class_B_peak", "class_B_std", "class_B_early_ratio", "class_B_medium_ratio", "class_B_long_ratio", "class_G_total", "class_G_peak", "class_G_std", "class_G_early_ratio", "class_G_medium_ratio", "class_G_long_ratio", "class_H_total", "class_H_peak", "class_H_std", "class_H_early_ratio", "class_H_medium_ratio", "class_H_long_ratio", "class_K_total", "class_K_peak", "class_K_std", "class_K_early_ratio", "class_K_medium_ratio", "class_K_long_ratio", "class_L_total", "class_L_peak", "class_L_std", "class_L_early_ratio", "class_L_medium_ratio", "class_L_long_ratio", "class_M_total", "class_M_peak", "class_M_std", "class_M_early_ratio", "class_M_medium_ratio", "class_M_long_ratio", "class_Q_total", "class_Q_peak", "class_Q_std", "class_Q_early_ratio", "class_Q_medium_ratio", "class_Q_long_ratio", "class_S_total", "class_S_peak", "class_S_std", "class_S_early_ratio", "class_S_medium_ratio", "class_S_long_ratio", "class_T_total", "class_T_peak", "class_T_std", "class_T_early_ratio", "class_T_medium_ratio", "class_T_long_ratio", "class_V_total", "class_V_peak", "class_V_std", "class_V_early_ratio", "class_V_medium_ratio", "class_V_long_ratio", "class_Y_total", "class_Y_peak", "class_Y_std", "class_Y_early_ratio", "class_Y_medium_ratio", "class_Y_long_ratio", "rd_bookings_std", "has_historical_data", "avg_fleet_seats", "max_fleet_seats", "min_fleet_seats", "fleet_aircraft_types", "competition_score", "hub_connection_score", "seat_capacity", "capacity_ratio", "time_offset_magnitude", "route_frequency", "seats_per_hour", "departure_period_encoded", "departure_period_freq", "arrival_period_encoded", "arrival_period_freq", "competition_level_encoded", "competition_level_freq", "flight_type_encoded", "flight_type_freq", "route_encoded", "route_freq"], "mean_target": 97.18888036807056, "std_target": 62.99315792559665, "min_target": 0.0, "max_target": 284.24}