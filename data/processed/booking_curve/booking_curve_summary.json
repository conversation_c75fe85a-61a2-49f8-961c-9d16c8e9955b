{"full_curve": {"prediction_type": "full_curve", "description": "预测全部33个RD时间点的预订量", "input_features": 12, "output_dimensions": 33, "train_samples": 37752, "test_samples": 9438, "rd_timepoints": ["RD0", "RD1", "RD2", "RD4", "RD6", "RD7", "RD8", "RD11", "RD14", "RD16", "RD18", "RD21", "RD24", "RD28", "RD33", "RD39", "RD44", "RD49", "RD59", "RD69", "RD79", "RD89", "RD99", "RD119", "RD139", "RD159", "RD179", "RD209", "RD239", "RD269", "RD330"], "feature_names": ["flight_duration", "departure_hour", "arrival_hour", "weekend_flight", "dep_offset", "arr_offset", "fare", "market_share", "is_hub_origin", "is_hub_destination", "is_hub_to_hub", "cabin_class_encoded"]}, "window_aggregate": {"prediction_type": "window_aggregate", "description": "预测早期、中期、长期三个时间窗口的累计预订量", "input_features": 12, "output_dimensions": 3, "train_samples": 37752, "test_samples": 9438, "time_windows": {"early": ["RD0", "RD1", "RD2", "RD4", "RD6"], "medium": ["RD7", "RD8", "RD11", "RD14", "RD16", "RD18", "RD21", "RD24", "RD28"], "long": ["RD33", "RD39", "RD44", "RD49", "RD59", "RD69", "RD79", "RD89", "RD99"]}, "window_names": ["early", "medium", "long"], "feature_names": ["flight_duration", "departure_hour", "arrival_hour", "weekend_flight", "dep_offset", "arr_offset", "fare", "market_share", "is_hub_origin", "is_hub_destination", "is_hub_to_hub", "cabin_class_encoded"]}, "key_points": {"prediction_type": "key_points", "description": "预测6个关键时间点的预订量", "input_features": 12, "output_dimensions": 6, "train_samples": 37752, "test_samples": 9438, "key_timepoints": ["RD0", "RD7", "RD14", "RD21", "RD28", "RD33"], "feature_names": ["flight_duration", "departure_hour", "arrival_hour", "weekend_flight", "dep_offset", "arr_offset", "fare", "market_share", "is_hub_origin", "is_hub_destination", "is_hub_to_hub", "cabin_class_encoded"]}, "final_demand": {"prediction_type": "final_demand", "description": "基于早期预订数据预测最终(RD99)预订量", "input_features": 17, "output_dimensions": 1, "train_samples": 37752, "test_samples": 9438, "early_rds_used": ["RD0", "RD1", "RD2", "RD4", "RD6"], "target_rd": "RD99", "feature_names": ["early_RD0", "early_RD1", "early_RD2", "early_RD4", "early_RD6", "flight_duration", "departure_hour", "arrival_hour", "weekend_flight", "dep_offset", "arr_offset", "fare", "market_share", "is_hub_origin", "is_hub_destination", "is_hub_to_hub", "cabin_class_encoded"]}}