{"cabin_class": "M", "target_variable": "class_M_total", "n_features": 33, "train_samples": 652, "test_samples": 163, "features": ["departure_time", "arrival_time", "departure_hour", "arrival_hour", "flight_duration", "dep_offset", "arr_offset", "market_share", "is_hub_origin", "is_hub_destination", "is_hub_to_hub", "same_airport", "is_multi_stop", "has_historical_data", "avg_fleet_seats", "max_fleet_seats", "min_fleet_seats", "fleet_aircraft_types", "departure_period_encoded", "arrival_period_encoded", "weekend_flight_encoded", "competition_level_encoded", "flight_type_encoded", "cabin_demand_strength", "cabin_volatility", "cabin_concentration", "cabin_active", "route_M_avg", "route_M_std", "route_M_max", "period_cabin_avg", "competition_cabin_avg", "cabin_share_ratio"], "mean_target": 1.6317893660490799, "std_target": 3.0451980786605826, "min_target": 0.0, "max_target": 20.21, "cabin_flights_count": 404, "cabin_demand_ratio": 0.49570552147239266, "all_zero_demand": false}