{"B": {"cabin_class": "B", "target_variable": "class_B_total", "n_features": 33, "train_samples": 652, "test_samples": 163, "features": ["departure_time", "arrival_time", "departure_hour", "arrival_hour", "flight_duration", "dep_offset", "arr_offset", "market_share", "is_hub_origin", "is_hub_destination", "is_hub_to_hub", "same_airport", "is_multi_stop", "has_historical_data", "avg_fleet_seats", "max_fleet_seats", "min_fleet_seats", "fleet_aircraft_types", "departure_period_encoded", "arrival_period_encoded", "weekend_flight_encoded", "competition_level_encoded", "flight_type_encoded", "cabin_demand_strength", "cabin_volatility", "cabin_concentration", "cabin_active", "route_B_avg", "route_B_std", "route_B_max", "period_cabin_avg", "competition_cabin_avg", "cabin_share_ratio"], "mean_target": 1.321451942739264, "std_target": 3.0456897383249126, "min_target": 0.0, "max_target": 48.68, "cabin_flights_count": 370, "cabin_demand_ratio": 0.4539877300613497, "all_zero_demand": false}, "G": {"cabin_class": "G", "target_variable": "class_G_total", "n_features": 33, "train_samples": 652, "test_samples": 163, "features": ["departure_time", "arrival_time", "departure_hour", "arrival_hour", "flight_duration", "dep_offset", "arr_offset", "market_share", "is_hub_origin", "is_hub_destination", "is_hub_to_hub", "same_airport", "is_multi_stop", "has_historical_data", "avg_fleet_seats", "max_fleet_seats", "min_fleet_seats", "fleet_aircraft_types", "departure_period_encoded", "arrival_period_encoded", "weekend_flight_encoded", "competition_level_encoded", "flight_type_encoded", "cabin_demand_strength", "cabin_volatility", "cabin_concentration", "cabin_active", "route_G_avg", "route_G_std", "route_G_max", "period_cabin_avg", "competition_cabin_avg", "cabin_share_ratio"], "mean_target": 7.592576687111963, "std_target": 15.893184603872703, "min_target": 0.0, "max_target": 123.05, "cabin_flights_count": 622, "cabin_demand_ratio": 0.7631901840490798, "all_zero_demand": false}, "H": {"cabin_class": "H", "target_variable": "class_H_total", "n_features": 33, "train_samples": 652, "test_samples": 163, "features": ["departure_time", "arrival_time", "departure_hour", "arrival_hour", "flight_duration", "dep_offset", "arr_offset", "market_share", "is_hub_origin", "is_hub_destination", "is_hub_to_hub", "same_airport", "is_multi_stop", "has_historical_data", "avg_fleet_seats", "max_fleet_seats", "min_fleet_seats", "fleet_aircraft_types", "departure_period_encoded", "arrival_period_encoded", "weekend_flight_encoded", "competition_level_encoded", "flight_type_encoded", "cabin_demand_strength", "cabin_volatility", "cabin_concentration", "cabin_active", "route_H_avg", "route_H_std", "route_H_max", "period_cabin_avg", "competition_cabin_avg", "cabin_share_ratio"], "mean_target": 2.1867484662576686, "std_target": 3.966954340356958, "min_target": 0.0, "max_target": 32.09, "cabin_flights_count": 499, "cabin_demand_ratio": 0.6122699386503068, "all_zero_demand": false}, "K": {"cabin_class": "K", "target_variable": "class_K_total", "n_features": 33, "train_samples": 652, "test_samples": 163, "features": ["departure_time", "arrival_time", "departure_hour", "arrival_hour", "flight_duration", "dep_offset", "arr_offset", "market_share", "is_hub_origin", "is_hub_destination", "is_hub_to_hub", "same_airport", "is_multi_stop", "has_historical_data", "avg_fleet_seats", "max_fleet_seats", "min_fleet_seats", "fleet_aircraft_types", "departure_period_encoded", "arrival_period_encoded", "weekend_flight_encoded", "competition_level_encoded", "flight_type_encoded", "cabin_demand_strength", "cabin_volatility", "cabin_concentration", "cabin_active", "route_K_avg", "route_K_std", "route_K_max", "period_cabin_avg", "competition_cabin_avg", "cabin_share_ratio"], "mean_target": 8.995245398773006, "std_target": 21.782812058330887, "min_target": 0.0, "max_target": 158.15, "cabin_flights_count": 586, "cabin_demand_ratio": 0.7190184049079754, "all_zero_demand": false}, "L": {"cabin_class": "L", "target_variable": "class_L_total", "n_features": 33, "train_samples": 652, "test_samples": 163, "features": ["departure_time", "arrival_time", "departure_hour", "arrival_hour", "flight_duration", "dep_offset", "arr_offset", "market_share", "is_hub_origin", "is_hub_destination", "is_hub_to_hub", "same_airport", "is_multi_stop", "has_historical_data", "avg_fleet_seats", "max_fleet_seats", "min_fleet_seats", "fleet_aircraft_types", "departure_period_encoded", "arrival_period_encoded", "weekend_flight_encoded", "competition_level_encoded", "flight_type_encoded", "cabin_demand_strength", "cabin_volatility", "cabin_concentration", "cabin_active", "route_L_avg", "route_L_std", "route_L_max", "period_cabin_avg", "competition_cabin_avg", "cabin_share_ratio"], "mean_target": 4.832479550095092, "std_target": 11.167532207081692, "min_target": 0.0, "max_target": 100.37, "cabin_flights_count": 540, "cabin_demand_ratio": 0.6625766871165644, "all_zero_demand": false}, "M": {"cabin_class": "M", "target_variable": "class_M_total", "n_features": 33, "train_samples": 652, "test_samples": 163, "features": ["departure_time", "arrival_time", "departure_hour", "arrival_hour", "flight_duration", "dep_offset", "arr_offset", "market_share", "is_hub_origin", "is_hub_destination", "is_hub_to_hub", "same_airport", "is_multi_stop", "has_historical_data", "avg_fleet_seats", "max_fleet_seats", "min_fleet_seats", "fleet_aircraft_types", "departure_period_encoded", "arrival_period_encoded", "weekend_flight_encoded", "competition_level_encoded", "flight_type_encoded", "cabin_demand_strength", "cabin_volatility", "cabin_concentration", "cabin_active", "route_M_avg", "route_M_std", "route_M_max", "period_cabin_avg", "competition_cabin_avg", "cabin_share_ratio"], "mean_target": 1.6317893660490799, "std_target": 3.0451980786605826, "min_target": 0.0, "max_target": 20.21, "cabin_flights_count": 404, "cabin_demand_ratio": 0.49570552147239266, "all_zero_demand": false}, "Q": {"cabin_class": "Q", "target_variable": "class_Q_total", "n_features": 33, "train_samples": 652, "test_samples": 163, "features": ["departure_time", "arrival_time", "departure_hour", "arrival_hour", "flight_duration", "dep_offset", "arr_offset", "market_share", "is_hub_origin", "is_hub_destination", "is_hub_to_hub", "same_airport", "is_multi_stop", "has_historical_data", "avg_fleet_seats", "max_fleet_seats", "min_fleet_seats", "fleet_aircraft_types", "departure_period_encoded", "arrival_period_encoded", "weekend_flight_encoded", "competition_level_encoded", "flight_type_encoded", "cabin_demand_strength", "cabin_volatility", "cabin_concentration", "cabin_active", "route_Q_avg", "route_Q_std", "route_Q_max", "period_cabin_avg", "competition_cabin_avg", "cabin_share_ratio"], "mean_target": 2.7775613496901843, "std_target": 5.069239567621686, "min_target": 0.0, "max_target": 34.41, "cabin_flights_count": 507, "cabin_demand_ratio": 0.6220858895705521, "all_zero_demand": false}, "S": {"cabin_class": "S", "target_variable": "class_S_total", "n_features": 33, "train_samples": 652, "test_samples": 163, "features": ["departure_time", "arrival_time", "departure_hour", "arrival_hour", "flight_duration", "dep_offset", "arr_offset", "market_share", "is_hub_origin", "is_hub_destination", "is_hub_to_hub", "same_airport", "is_multi_stop", "has_historical_data", "avg_fleet_seats", "max_fleet_seats", "min_fleet_seats", "fleet_aircraft_types", "departure_period_encoded", "arrival_period_encoded", "weekend_flight_encoded", "competition_level_encoded", "flight_type_encoded", "cabin_demand_strength", "cabin_volatility", "cabin_concentration", "cabin_active", "route_S_avg", "route_S_std", "route_S_max", "period_cabin_avg", "competition_cabin_avg", "cabin_share_ratio"], "mean_target": 0.6053220858895705, "std_target": 2.0892625178401003, "min_target": 0.0, "max_target": 32.279999999999994, "cabin_flights_count": 262, "cabin_demand_ratio": 0.3214723926380368, "all_zero_demand": false}, "T": {"cabin_class": "T", "target_variable": "class_T_total", "n_features": 33, "train_samples": 652, "test_samples": 163, "features": ["departure_time", "arrival_time", "departure_hour", "arrival_hour", "flight_duration", "dep_offset", "arr_offset", "market_share", "is_hub_origin", "is_hub_destination", "is_hub_to_hub", "same_airport", "is_multi_stop", "has_historical_data", "avg_fleet_seats", "max_fleet_seats", "min_fleet_seats", "fleet_aircraft_types", "departure_period_encoded", "arrival_period_encoded", "weekend_flight_encoded", "competition_level_encoded", "flight_type_encoded", "cabin_demand_strength", "cabin_volatility", "cabin_concentration", "cabin_active", "route_T_avg", "route_T_std", "route_T_max", "period_cabin_avg", "competition_cabin_avg", "cabin_share_ratio"], "mean_target": 11.8960736196319, "std_target": 24.63594367921363, "min_target": 0.0, "max_target": 130.20999999999998, "cabin_flights_count": 635, "cabin_demand_ratio": 0.7791411042944786, "all_zero_demand": false}, "V": {"cabin_class": "V", "target_variable": "class_V_total", "n_features": 33, "train_samples": 652, "test_samples": 163, "features": ["departure_time", "arrival_time", "departure_hour", "arrival_hour", "flight_duration", "dep_offset", "arr_offset", "market_share", "is_hub_origin", "is_hub_destination", "is_hub_to_hub", "same_airport", "is_multi_stop", "has_historical_data", "avg_fleet_seats", "max_fleet_seats", "min_fleet_seats", "fleet_aircraft_types", "departure_period_encoded", "arrival_period_encoded", "weekend_flight_encoded", "competition_level_encoded", "flight_type_encoded", "cabin_demand_strength", "cabin_volatility", "cabin_concentration", "cabin_active", "route_V_avg", "route_V_std", "route_V_max", "period_cabin_avg", "competition_cabin_avg", "cabin_share_ratio"], "mean_target": 4.27329243353681, "std_target": 9.233797857279557, "min_target": 0.0, "max_target": 94.56000000000002, "cabin_flights_count": 551, "cabin_demand_ratio": 0.6760736196319018, "all_zero_demand": false}, "Y": {"cabin_class": "Y", "target_variable": "class_Y_total", "n_features": 33, "train_samples": 652, "test_samples": 163, "features": ["departure_time", "arrival_time", "departure_hour", "arrival_hour", "flight_duration", "dep_offset", "arr_offset", "market_share", "is_hub_origin", "is_hub_destination", "is_hub_to_hub", "same_airport", "is_multi_stop", "has_historical_data", "avg_fleet_seats", "max_fleet_seats", "min_fleet_seats", "fleet_aircraft_types", "departure_period_encoded", "arrival_period_encoded", "weekend_flight_encoded", "competition_level_encoded", "flight_type_encoded", "cabin_demand_strength", "cabin_volatility", "cabin_concentration", "cabin_active", "route_Y_avg", "route_Y_std", "route_Y_max", "period_cabin_avg", "competition_cabin_avg", "cabin_share_ratio"], "mean_target": 3.0660736196319016, "std_target": 5.771090107322003, "min_target": 0.0, "max_target": 80.36999999999999, "cabin_flights_count": 568, "cabin_demand_ratio": 0.6969325153374233, "all_zero_demand": false}}