{"master": {"tasks": [{"id": 1, "title": "项目需求理解与任务分析", "description": "深入理解航空公司机型分配优化项目的整体需求、目标和实施路径。分析项目涉及的核心业务场景（A航空公司并购B公司后的机型分配优化），明确技术实现路径（机器学习预测+运筹学优化），识别项目关键成功因素和价值指标。", "details": "项目需求理解过程：\n1. 分析项目核心目标：为815个每日航班和211架飞机（9种机型）制定优化的机型分配方案，最大化收益并降低成本\n2. 理解技术架构：需求预测模型（LSTM/XGBoost/GRU）+ 机型分配优化（MILP）的综合解决方案  \n3. 识别业务价值：年化收益提升¥4.5亿元，运营效率提升40%，决策时间从2小时缩短至15分钟\n4. 明确实施路径：数据预处理→需求预测→优化求解→分析评估→报告生成的完整工作流\n5. 确定关键指标：R²>0.8的预测精度，>15%的利润提升，完整的航班覆盖和机队平衡约束", "testStrategy": "", "status": "done", "dependencies": [], "priority": "high", "subtasks": []}, {"id": 2, "title": "数据集 detailed 分析与特征理解", "description": "对项目4个核心CSV数据文件进行全面源头分析，包括数据文件结构、特征内容、容量统计、业务关系和数据质量评估。生成详细的数据分析报告为后续开发提供技术基础。", "details": "数据集详细分析过程：\n1. 文件概览分析：识别4个核心文件（fleet.csv, schedule.csv, market_share.csv, products.csv）的容量（总5.3MB）和记录数分布\n2. 机队数据解析：分析9种机型的舱位配置编码（F/C/Y）、座位数（48-165座）、飞机数量分布和运营成本参数\n3. 航班网络分析：解析815个航班在85个机场间的分布，识别主要枢纽（TFB、QEY、MBY）和时区偏移特征\n4. 市场竞争分析：分析819个航线的市场份额分布（垄断19.1%、强势28.6%、竞争35.3%、弱势17.0%）\n5. 产品销售分析：深度解析47,190条记录的12种舱位等级、31个预订距离特征（RD0-RD330）和经停航班逻辑\n6. 特征依赖分析：构建机场代码匹配、时区转换、业务逻辑约束的数据完整性网络\n7. 数据质量评估：识别数据完整性、一致性、平衡性优势和时区处理、高维数据等技术挑战", "testStrategy": "", "status": "done", "dependencies": [], "priority": "high", "subtasks": [{"id": 1, "title": "数据文件基本信息统计分析", "description": "统计4个核心CSV文件的容量（总5.3MB）、记录数分布、格式验证和基本数据质量检查", "details": "完成数据文件的基本统计分析：\n- 文件大小统计：fleet.csv(250B), schedule.csv(29KB), market_share.csv(16KB), products.csv(5.28MB)\n- 记录数统计：fleet(9行), schedule(815行), market_share(819行), products(47,190行)\n- 格式验证：确认所有文件为CSV格式，文件结构完整\n- 数据完整性检查：验证标题行和数据行的对应关系", "status": "done", "dependencies": [], "parentTaskId": 2}, {"id": 2, "title": "各数据文件特征详解与业务含义解析", "description": "详细分析4个数据文件的所有特征字段、数据类型、取值范围和业务含义，包括机队编码解析、航班网络结构、市场份额分布和产品销售模式", "details": "深度解析各数据文件特征：\n- 机队数据：9种机型F/C/Y编码解析、座位数分布(48-165座)、飞机数量统计、成本参数分析\n- 航班时刻表：航班号分析、85个机场网络拓扑、主要枢纽识别(TFB/QEY/MBY)、时区偏移处理\n- 市场份额：819个航线的竞争环境分析、市场份额等级划分(垄断/强势/竞争/弱势)\n- 产品数据：47,190条记录的12种舱位等级、31个预订距离特征RD0-RD330的时序特征、经停航班逻辑", "status": "done", "dependencies": [], "parentTaskId": 2}, {"id": 3, "title": "特征间关系和依赖网络分析", "description": "分析4个数据文件间的业务逻辑依赖关系，构建空间维度（机场匹配）、时间维度（UTC转换）和业务逻辑（座位约束、成本收益）的依赖网络", "details": "构建特征间依赖关系网络：\n- 空间维度依赖：85个机场代码在所有文件中的一致性验证，航线对匹配关系\n- 时间维度依赖：航班时刻的UTC时区转换逻辑，预订距离时序模式分析\n- 业务逻辑依赖：机队座位容量约束、成本收益平衡、市场份额影响机制\n- 数据完整性验证：航班-机队-市场-产品的完整业务链条验证矩阵\n- 关键依赖风险识别：机场代码匹配、航线对一致性、舱位兼容性等关键依赖点", "status": "done", "dependencies": [], "parentTaskId": 2}, {"id": 4, "title": "数据质量评估与挑战识别", "description": "评估数据集的整体质量优势（完整性、平衡性、一致性），识别技术处理挑战（时区处理、高维数据、经停航班逻辑），生成分析报告文档", "details": "完成数据质量全面评估：\n- 数据质量优势：完整性(100%记录覆盖)、平衡性(各舱位4,290条)、一致性(机场代码统一)、时效性(完整时空覆盖)\n- 技术挑战识别：数据规模挑战(5.28MB产品文件)、时区处理挑战(UTC偏移转换)、经停航班逻辑处理、RD特征高维建模\n- 质量指标评级：完整性/准确性/一致性/时效性/唯一性均为优秀，有效性有待验证\n- 优化建议：数据预处理优先级排定、特征工程重点方向、模型设计考虑因素\n- 生成详细分析报告：创建docs/data_analysis_report.md文件 documenting完整数据分析结果", "status": "done", "dependencies": [], "parentTaskId": 2}]}, {"id": 3, "title": "需求预测模型开发与训练", "description": "根据最新的业务优先级分析，调整模型开发顺序。首先聚焦于最高优先级的预测任务：1）航班总需求预测 和 2）分舱位需求预测，这两项是MILP优化模型的核心约束。其次，开发中等优先级的任务：3）预订曲线预测 和 4）经停航班整体需求预测。最后，进行模型集成与接口开发，确保为815个航班与211架飞机的优化分配提供可靠的座位容量约束，支撑>15%的利润提升目标。", "status": "in-progress", "dependencies": [], "priority": "high", "details": "需求预测模型开发的核心任务，已按优先级重新排序：\n1. 航班总需求预测 (极高优先级): 基于历史数据和多维特征，预测每个航班的总体座位需求量，是优化模型的核心约束数据。\n2. 分舱位需求预测 (极高优先级): 实现11种舱位等级(B, G, H, K, L, M, Q, S, T, V, Y)的差异化需求预测，为座位容量约束提供精确数据。\n3. 预订曲线与经停航班预测 (中等优先级): 基于RD0-RD330时序特征预测需求变化趋势，并专门处理经停航班等复杂场景下的需求模式。\n4. 多模型对比与集成: 开发并对比XGBoost、LSTM/GRU等模型，选择或融合最优算法以提高预测精度。\n5. 优化模型接口对接: 确保预测输出（需求量、置信区间）精准对接MILP模型的容量约束，达成R²>0.8的技术精度目标。", "testStrategy": "1. 离线评估：使用时间序列交叉验证，在测试集上评估各预测目标（航班总需求、分舱位需求等）的R²、RMSE、MAE指标。\n2. 场景测试：针对经停航班、新开航线、主要市场等特殊场景，验证模型的预测准确性和鲁棒性。\n3. 接口测试：对预测模型与MILP优化模型的接口进行端到端测试，确保数据格式、内容和传输的正确性。\n4. A/B测试：在模拟环境中，对比基于新预测模型的优化方案与基线方案的收益提升效果。", "subtasks": [{"id": 1, "title": "数据预处理与特征工程", "description": "对4个核心CSV文件进行数据清洗、整合和特征工程，为航班总需求、分舱位需求、预订曲线等7大预测目标构建高质量的输入数据集。", "status": "done", "dependencies": [], "details": "数据预处理与特征工程的关键工作：\n1. 数据加载与验证：加载fleet.csv、schedule.csv、market_share.csv、products.csv，验证数据完整性和一致性\n2. 时间特征处理：基于schedule.csv的depoff/arroff进行UTC时区转换，计算航班时长、出发时间段、星期几等时间特征\n3. 航线网络特征：构建85个机场的网络拓扑特征，识别主要枢纽(TFB/QEY/MBY)，计算航线距离和繁忙度\n4. 机队约束特征：解析9种机型的F/C/Y编码，提取座位数、成本参数、舱位容量约束等特征\n5. 市场竞争特征：整合market_share.csv数据，划分市场等级(垄断/强势/竞争/弱势)，计算竞争强度指标\n6. RD时序特征工程：从products.csv的31个RD字段提取时间序列特征，用于预订曲线预测\n7. 特征整合与标准化：将所有特征整合为统一的训练数据集，处理缺失值，进行特征标准化和编码\n8. 目标变量构建：根据预测目标（航班总需求、分舱位需求等）生成相应的标签数据\n9. 数据集划分：按时间序列原则划分训练集、验证集、测试集，避免数据泄露", "testStrategy": ""}, {"id": 2, "title": "航班总需求预测与对比模型开发 (XGBoost)", "description": "开发XGBoost模型，重点预测每个航班的座位总需求，并将其作为基准模型与时序模型进行综合对比，确保选择最优预测算法。", "status": "in-progress", "dependencies": [], "details": "对比模型开发的关键任务：\n1. XGBoost模型开发：\n   - 重点用于预测每个航班的座位总需求，利用其对结构化数据的强大处理能力。\n   - 特征重要性分析：使用XGBoost内置特征重要性识别关键预测因素。\n   - 超参数优化：通过网格搜索或贝叶斯优化调优树的数量、深度、学习率等关键参数。\n   - 交叉验证：使用K折交叉验证评估模型泛化能力。\n\n2. 模型对比分析：\n   - 性能指标对比：在相同测试集上对比各模型（LSTM/GRU vs XGBoost）在不同预测目标上的R²、RMSE、MAE等指标。\n   - 训练效率对比：分析训练时间、内存使用、计算资源消耗。\n   - 预测稳定性对比：评估模型在不同数据分布下的稳定性表现。\n   - 业务适用性对比：从业务解释性、部署难度、维护成本等维度对比。\n\n3. 最佳模型选择：基于综合对比结果，为不同预测目标（总需求、分舱位需求）选择最适合的预测模型或模型融合策略。\n<info added on 2025-07-31T13:29:51.650Z>\nThe XGBoost model for total flight demand prediction has been successfully completed.\n\nModel Performance:\n- Test Set: R²=0.9103, RMSE=18.67, MAE=12.84, MAPE=21.02%.\n- Cross-Validation: R² of 0.9199 ± 0.0089, indicating strong generalization.\n\nTechnical Implementation & Findings:\n- Optimal hyperparameters were identified via GridSearchCV: learning_rate=0.1, max_depth=6, n_estimators=100, subsample=0.8.\n- Feature importance analysis revealed 'rd_bookings_std' (booking standard deviation) as the most critical predictor, with an importance of 50.37%.\n\nSaved Artifacts:\n- Model: xgboost_total_demand_model_20250731_212837.joblib\n- Results: training_results_20250731_212837.json\n- Feature Importance: feature_importance_20250731_212837.csv\n\nThis model is now ready to provide high-accuracy demand forecasts and serve as a performance benchmark for subsequent models.\n</info added on 2025-07-31T13:29:51.650Z>", "testStrategy": ""}, {"id": 3, "title": "时序与多目标预测模型开发 (LSTM/GRU)", "description": "基于RD0-RD330时序数据，开发LSTM/GRU神经网络模型，实现分舱位需求和预订曲线的精确预测，并探索多任务学习架构。", "status": "pending", "dependencies": [], "details": "LSTM/GRU模型开发的核心任务：\n1. 模型架构设计：设计处理RD时序数据的LSTM/GRU网络架构，可采用多任务学习架构，同时输出分舱位需求和预订曲线预测。\n2. 时序数据预处理：将RD0-RD330的31个时间步数据转换为适用的序列格式，处理变长序列和缺失值。\n3. 多目标预测建模：为11种舱位需求和预订曲线构建联合预测或独立预测模型。\n4. 模型训练优化：实现早停机制、学习率调度、梯度裁剪等训练策略，防止过拟合。\n5. 超参数调优：优化网络层数、隐藏单元数、dropout率、batch_size等关键超参数。\n6. 模型验证与评估：使用时间序列交叉验证方法，计算R²、RMSE、MAE等关键指标，确保时序预测任务达到目标精度。\n7. 预测结果保存：保存训练好的模型，支持批量预测和单样本预测两种模式。", "testStrategy": ""}, {"id": 4, "title": "集成模型与不确定性量化", "description": "开发集成学习策略，整合多个模型对航班总需求和分舱位需求的预测结果，并进行不确定性量化，为优化模型提供更鲁棒的输入。", "status": "pending", "dependencies": [], "details": "集成模型与不确定性量化的核心工作：\n1. 集成学习策略：\n   - 加权集成：基于模型性能，实现对航班总需求、分舱位需求等多个预测目标的加权集成。\n   - Stacking集成：训练元学习器学习各模型预测的最优组合方式。\n   - 动态权重调整：根据预测任务的复杂度动态调整各模型的权重分配。\n\n2. 不确定性量化方法：\n   - 分位数回归：训练分位数回归模型（如LightGBM的Quantile Regression）预测不同置信水平的需求区间。\n   - 集成不确定性：利用多个模型的预测差异作为不确定性指标。\n   - 蒙特卡洛Dropout：在推理时启用Dropout生成多个预测样本估计不确定性。\n\n3. 置信区间计算与校准：\n   - 提供90%、95%、99%等多个置信水平选择。\n   - 验证置信区间的实际覆盖率是否符合设定的置信水平。\n\n4. 风险评估报告：\n   - 基于不确定性程度为优化决策提供风险管控建议，输出乐观、悲观、基准等多场景的需求预测。", "testStrategy": ""}, {"id": 5, "title": "优化模型接口开发与验证", "description": "开发需求预测模型与MILP优化模型的接口，确保预测的“每个航班座位需求数量”和“各舱位等级座位需求量”能够无缝对接MILP优化模型的“座位容量约束”。", "status": "pending", "dependencies": [], "details": "优化模型接口开发的核心任务：\n1. 接口需求分析：\n   - 明确MILP模型对座位容量约束（capacity constraints）的数据格式和精度要求。\n   - 设计预测结果的数据结构，包含需求预测值、置信区间、质量指标等。\n\n2. 需求预测输出接口：\n   - 航班总需求输出：提供每个航班的预测座位需求量。\n   - 分舱位需求输出：提供每个航班下11个舱位等级的预测需求量。\n   - 预订曲线数据输出：提供预订过程中的需求动态，供高级优化策略使用。\n\n3. 约束条件生成接口：\n   - 容量约束生成：基于预测的航班总需求和分舱位需求，生成MILP模型所需的容量约束参数。\n   - 风险约束生成：基于预测置信区间生成鲁棒优化所需的约束条件。\n\n4. 集成验证测试：\n   - 端到端测试：验证从原始数据输入到生成MILP约束参数的完整流程。\n   - 性能基准测试：测试预测接口在815航班规模下的性能表现。\n   - 数据一致性验证：确保预测结果与优化模型输入的语义一致性。\n\n5. 性能优化：\n   - 推理延迟优化：优化模型推理速度，满足实时预测需求。\n   - 批处理支持：实现批量预测功能，提高处理效率。", "testStrategy": ""}]}], "metadata": {"created": "2025-06-19T11:07:26.446Z", "updated": "2025-07-31T13:11:02.942Z", "description": "Tasks for master context"}}}