# Task ID: 3
# Title: 需求预测模型开发与训练
# Status: pending
# Dependencies: None
# Priority: high
# Description: 基于需求预测目标分析，开发高精度需求预测模型，实现基于航班信息、航线特征、市场份额和历史预订模式的多维特征预测，为815个航班与211架飞机的优化分配提供可靠的收益计算和约束条件基础。目标达成R²>0.8的预测精度，支撑>15%的利润提升目标。
# Details:
需求预测模型开发的核心任务：
1. 基于历史产品数据(products.csv)的RD0-RD330时序特征，建立精确的舱位等级需求预测模型
2. 整合航班时刻表(schedule.csv)、机队配置(fleet.csv)、市场份额(market_share.csv)的多维特征
3. 开发LSTM、XGBoost、GRU多模型对比框架，选择最佳预测算法
4. 实现12种舱位等级(B, G, H, K, L, M, Q, S, T, V, Y)的差异化建模策略
5. 建立预测结果与优化模型的接口，确保需求预测输出完全匹配优化输入需求
6. 达成R²>0.8的技术精度目标，支撑整体项目的业务价值实现

# Test Strategy:


# Subtasks:
## 1. 数据预处理与特征工程 [pending]
### Dependencies: None
### Description: 对4个核心CSV文件进行数据清洗、整合和特征工程，提取RD时序特征、航班时间特征、航线网络特征、市场份额特征等，为模型训练准备高质量输入数据
### Details:
数据预处理与特征工程的关键工作：
1. 数据加载与验证：加载fleet.csv、schedule.csv、market_share.csv、products.csv，验证数据完整性和一致性
2. 时间特征处理：基于schedule.csv的depoff/arroff进行UTC时区转换，计算航班时长、出发时间段、星期几等时间特征
3. 航线网络特征：构建85个机场的网络拓扑特征，识别主要枢纽(TFB/QEY/MBY)，计算航线距离和繁忙度
4. 机队约束特征：解析9种机型的F/C/Y编码，提取座位数、成本参数、舱位容量约束等特征
5. 市场竞争特征：整合market_share.csv数据，划分市场等级(垄断/强势/竞争/弱势)，计算竞争强度指标
6. RD时序特征工程：从products.csv的31个RD字段提取时间序列特征，包括统计特征、分布特征、趋势特征等
7. 特征整合与标准化：将所有特征整合为统一的训练数据集，处理缺失值，进行特征标准化和编码
8. 数据集划分：按时间序列原则划分训练集、验证集、测试集，避免数据泄露

## 2. LSTM时序预测模型开发 [pending]
### Dependencies: None
### Description: 基于RD0-RD330预订距离时序数据，开发LSTM神经网络模型，实现12种舱位等级的高精度需求预测，重点关注时序模式和预订曲线的学习
### Details:
LSTM模型开发的核心任务：
1. 模型架构设计：设计专门处理RD时序数据的LSTM网络架构，包括输入层、LSTM隐藏层、注意力机制、输出层
2. 时序数据预处理：将RD0-RD330的31个时间步数据转换为LSTM适用的序列格式，处理变长序列和缺失值
3. 舱位差异化建模：为12种舱位等级(B, G, H, K, L, M, Q, S, T, V, Y)分别训练或设计多输出LSTM模型
4. 模型训练优化：实现早停机制、学习率调度、梯度裁剪等训练策略，防止过拟合和梯度爆炸
5. 超参数调优：优化LSTM层数、隐藏单元数、dropout率、batch_size等关键超参数
6. 模型验证与评估：使用时间序列交叉验证方法，计算R²、RMSE、MAE等关键指标，确保达到R²>0.8目标
7. 预测结果保存：保存训练好的LSTM模型，支持批量预测和单样本预测两种模式

## 3. XGBoost/GRU对比模型开发 [pending]
### Dependencies: None
### Description: 开发XGBoost和GRU模型作为LSTM的对比方案，分别处理结构化特征数据和时序特征，通过多模型对比确保选择最优预测算法
### Details:
对比模型开发的关键任务：
1. XGBoost模型开发：
   - 特征重要性分析：使用XGBoost内置特征重要性识别关键预测因素
   - 结构化特征处理：重点处理时间特征、航线特征、市场特征等非时序特征
   - 超参数优化：调优树的数量、深度、学习率等关键参数
   - 交叉验证：使用K折交叉验证评估模型泛化能力
   
2. GRU时序模型开发：
   - GRU架构设计：设计比LSTM更轻量的时序模型架构
   - 训练效率优化：利用GRU训练速度快的优势进行快速迭代
   - 多时步预测：实现基于历史时序的滚动预测功能
   - 模型压缩：探索模型量化和剪枝技术降低推理复杂度
   
3. 模型对比分析：
   - 性能指标对比：在相同测试集上对比R²、RMSE、MAE、MAPE等指标
   - 训练效率对比：分析训练时间、内存使用、计算资源消耗
   - 预测稳定性对比：评估模型在不同数据分布下的稳定性表现
   - 业务适用性对比：从业务解释性、部署难度、维护成本等维度对比
   
4. 最佳模型选择：基于综合对比结果，选择最适合本项目的预测模型架构

## 4. 集成模型与不确定性量化 [pending]
### Dependencies: None
### Description: 开发集成学习策略整合多个模型预测结果，实现量化的不确定性评估，提供预测置信区间和风险评估，支撑优化模型的鲁棒性设计
### Details:
集成模型与不确定性量化的核心工作：
1. 集成学习策略：
   - 加权集成：基于模型性能计算动态权重，实现LSTM、XGBoost、GRU的加权集成
   - Stacking集成：训练元学习器学习各模型预测的最优组合方式
   - 动态权重调整：根据预测任务的复杂度动态调整各模型的权重分配
   - 集成效果验证：验证集成模型相比单一模型的性能提升效果

2. 不确定性量化方法：
   - 贝叶斯神经网络：为LSTM模型添加贝叶斯层，预测结果的后验分布
   - 分位数回归：训练分位数回归模型预测不同置信水平的需求区间
   - 集成不确定性：利用多个模型的预测差异作为不确定性指标
   - 蒙特卡洛Dropout：在推理时启用Dropout生成多个预测样本估计不确定性

3. 置信区间计算：
   - 置信水平设置：提供90%、95%、99%等多个置信水平选择
   - 区间宽度优化：平衡预测精度和区间宽度，避免过宽或过窄的置信区间
   - 校准验证：验证置信区间的实际覆盖率是否符合设定的置信水平

4. 风险评估报告：
   - 预测质量评级：基于置信区间宽度为每个预测分配质量等级
   - 风险场景分析：提供乐观、悲观、基准等多场景的需求预测
   - 决策建议：基于不确定性程度为优化决策提供风险管控建议

## 5. 优化模型接口开发与验证 [pending]
### Dependencies: None
### Description: 开发需求预测模型与优化模型的完整接口，确保预测输出格式完全匹配优化输入需求，进行端到端的集成验证和性能测试
### Details:
优化模型接口开发的核心任务：
1. 接口需求分析：
   - 优化输入格式分析：明确优化模型对需求预测的具体输入格式要求
   - 数据结构设计：设计预测结果的数据结构，包含需求数量、置信区间、质量指标等
   - 性能要求定义：确定接口的响应时间、并发能力、内存占用等技术要求

2. 收益计算接口：
   - 舱位收益计算：实现基于预测需求量和票价的收益计算逻辑
   - 总收益汇总：汇总所有航班舱位的总收益，支持不同机型分配方案的收益对比
   - 边际收益分析：计算不同舱位等级的边际收益贡献，支撑优化决策

3. 约束条件接口：
   - 容量约束生成：基于预测需求和机型舱位容量生成容量约束条件
   - 风险约束生成：基于预测置信区间生成鲁棒性约束条件
   - 约束格式转换：将预测相关的约束转换为优化模型可接受的格式

4. 集成验证测试：
   - 端到端测试：验证从原始数据到优化结果的完整流程
   - 性能基准测试：测试预测接口在815航班规模下的性能表现
   - 异常处理验证：验证接口在异常情况下的容错能力和恢复机制
   - 数据一致性验证：确保预测结果与优化模型输入的语义一致性

5. 性能优化：
   - 推理延迟优化：优化模型推理速度，满足实时预测需求
   - 内存使用优化：减少预测过程中的内存占用
   - 批处理支持：实现批量预测功能，提高处理效率
   - 缓存机制：为重复预测结果实现智能缓存

