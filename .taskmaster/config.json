{"models": {"main": {"provider": "openai", "modelId": "gemini-2.5-pro", "baseURL": "http://**************:2999/v1", "maxTokens": 65536, "temperature": 0.2}, "research": {"provider": "openai", "modelId": "gemini-2.5-pro", "baseURL": "http://**************:2999/v1", "maxTokens": 8700, "temperature": 0.1}, "fallback": {"provider": "openai", "modelId": "gemini-2.5-pro", "baseURL": "http://**************:2999/v1", "maxTokens": 8192, "temperature": 0.1}}, "global": {"logLevel": "info", "debug": false, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "azureOpenaiBaseURL": "https://your-endpoint.openai.azure.com/", "userId": "**********", "defaultTag": "master"}}